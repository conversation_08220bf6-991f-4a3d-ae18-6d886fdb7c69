<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">unsplash.com</domain>
        <domain includeSubdomains="true">images.unsplash.com</domain>
        <domain includeSubdomains="true">plus.unsplash.com</domain>
        <domain includeSubdomains="true">source.unsplash.com</domain>
        <domain includeSubdomains="true">picsum.photos</domain>
        <domain includeSubdomains="true">via.placeholder.com</domain>
        <domain includeSubdomains="true">example.com</domain>
        <domain includeSubdomains="true">httpbin.org</domain>
    </domain-config>
    
    <!-- Allow all HTTP traffic for development -->
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>
