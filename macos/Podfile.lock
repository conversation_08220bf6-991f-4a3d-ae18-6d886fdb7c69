PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/darwin
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin

SPEC CHECKSUMS:
  connectivity_plus: 4adf20a405e25b42b9c9f87feff8f4b6fde18a4e
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
