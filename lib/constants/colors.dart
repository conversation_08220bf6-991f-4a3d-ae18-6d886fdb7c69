import 'package:flutter/cupertino.dart';

class ColorConstants{
  static const Color black = Color(0xFF000000);
  static const Color darkBlack = Color(0xFF2B2B2B);
  static const Color grey = Color(0xFFD9D9D9);
  static const Color lightGrey = Color(0xFFF2F2F2);
  static const transparent = Color(0xFFFFFFFF);
  static const Color gunSmoke =  Color(0xFF878787);
  static const Color philippineSilver =  Color(0xFFB7B6B6);
  static const Color darkGrey =  Color(0xFFB7B7B7);
  static const Color borderColor =  Color(0xFF8F8F8F);
  static const Color textColor =  Color(0xFF5A5A5A);
  static const Color white =  Color(0xFFFFFFFF);
  static const Color cardBackground =  Color(0xFFF6F6F6);
  static const Color lightCardBackground =  Color(0xFFF4F4F4);
  static const Color disabledColor =  Color(0xFFE5E5E5);
  static const Color backgroundColor =  Color(0xFFE4E4E4);
  static const Color deepBlue =  Color(0xFF113984);
  static const Color brightGreen =  Color(0xFF2CBD00);
  static const Color cardBorderColor =  Color(0xFFBEBEBE);
  static const Color checkColor =  Color(0xFF01D716);
  static const Color checkBgColor =  Color(0xFFE8E8E8);
  static const Color appColor =  Color(0xFFEBEBEB);
  static const Color notificationColor =  Color(0xFF6F6F6F);
  static const Color notificationDividerColor =  Color(0xFFD4D4D4);
  static const Color notificationTextColor =  Color(0xFF0C0C0C);

  static const Color bottomNavigationColor =  Color(0xFFD8D8D8);
  static const Color textCategoryColor =  Color(0xFF383838);
  static const Color statusBarMySpaceColor =  Color(0xFFA3A3A3);
  static const Color highlightedColor =  Color(0xFFFFFF00);
}