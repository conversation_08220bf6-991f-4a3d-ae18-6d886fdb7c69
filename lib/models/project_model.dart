class ProjectModel {
  final String id;
  final String projectName;
  final String studioName;
  final String studioInitial;
  final String category;
  final String location;
  final String year;
  final String duration;
  final String area;
  final String cost;
  final String description;
  final List<String> images;
  final List<TeamMember> teamMembers;
  final List<Comment> comments;
  final List<ProjectElement> elements;
  final bool isFavorite;
  final bool isProjectOfTheDay;

  ProjectModel({
    required this.id,
    required this.projectName,
    required this.studioName,
    required this.studioInitial,
    required this.category,
    required this.location,
    required this.year,
    required this.duration,
    required this.area,
    required this.cost,
    required this.description,
    required this.images,
    required this.teamMembers,
    required this.comments,
    required this.elements,
    this.isFavorite = false,
    this.isProjectOfTheDay = false,
  });

  // Factory constructor for creating sample data
  factory ProjectModel.sample({
    String? id,
    String? projectName,
    String? studioName,
    String? studioInitial,
    bool isProjectOfTheDay = false,
  }) {
    return ProjectModel(
      id: id ?? DateTime.now().millisecondsSinceEpoch.toString(),
      projectName: projectName ?? "Open House",
      studioName: studioName ?? "MuseLAB",
      studioInitial: studioInitial ?? "M",
      category: "Residential",
      location: "Thane",
      year: "2024",
      duration: "4 Months",
      area: "780 Sq.ft",
      cost: "Rs. 44 Lakh",
      description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. "
          "Fusce convallis pellentesque metus id lacinia. Nunc "
          "dapibus pulvinar auctor. Duis nec sem at orci Lorem "
          "ipsum dolor sit amet, consectetur adipiscing elit. Fusce "
          "convallis pellentesque metus id lacinia. Nunc dapibus "
          "pulvinar auctor. Duis nec sem at",
      images: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
        'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
        'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80',
        'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80',
      ],
      teamMembers: TeamMember.sampleTeam(),
      comments: Comment.sampleComments(),
      elements: ProjectElement.sampleElements(),
      isProjectOfTheDay: isProjectOfTheDay,
    );
  }

  // Copy with method for updating properties
  ProjectModel copyWith({
    String? id,
    String? projectName,
    String? studioName,
    String? studioInitial,
    String? category,
    String? location,
    String? year,
    String? duration,
    String? area,
    String? cost,
    String? description,
    List<String>? images,
    List<TeamMember>? teamMembers,
    List<Comment>? comments,
    List<ProjectElement>? elements,
    bool? isFavorite,
    bool? isProjectOfTheDay,
  }) {
    return ProjectModel(
      id: id ?? this.id,
      projectName: projectName ?? this.projectName,
      studioName: studioName ?? this.studioName,
      studioInitial: studioInitial ?? this.studioInitial,
      category: category ?? this.category,
      location: location ?? this.location,
      year: year ?? this.year,
      duration: duration ?? this.duration,
      area: area ?? this.area,
      cost: cost ?? this.cost,
      description: description ?? this.description,
      images: images ?? this.images,
      teamMembers: teamMembers ?? this.teamMembers,
      comments: comments ?? this.comments,
      elements: elements ?? this.elements,
      isFavorite: isFavorite ?? this.isFavorite,
      isProjectOfTheDay: isProjectOfTheDay ?? this.isProjectOfTheDay,
    );
  }
}

class TeamMember {
  final String role;
  final String name;

  TeamMember({
    required this.role,
    required this.name,
  });

  static List<TeamMember> sampleTeam() {
    return [
      TeamMember(role: "Designer", name: "MuseLAB"),
      TeamMember(role: "Principle 1", name: "Huzefa Rangwala"),
      TeamMember(role: "Principle 2", name: "Jaseem Pirani"),
      TeamMember(role: "Designer 1", name: "Ashish Shah"),
      TeamMember(role: "Designer 2", name: "Rakesh Pai"),
      TeamMember(role: "PMC", name: "MIPL Ltd"),
      TeamMember(role: "Civil", name: "Afcon"),
      TeamMember(role: "Electrical", name: "Patel Electricals"),
      TeamMember(role: "Plumbing", name: "Infon"),
    ];
  }
}

class Comment {
  final String id;
  final String name;
  final String date;
  final String comment;
  final String avatarText;
  final String avatarColor;
  final String avatarTextColor;
  final int replyCount;

  Comment({
    required this.id,
    required this.name,
    required this.date,
    required this.comment,
    required this.avatarText,
    required this.avatarColor,
    required this.avatarTextColor,
    required this.replyCount,
  });

  static List<Comment> sampleComments() {
    return [
      Comment(
        id: "1",
        name: "Tantra Architects",
        date: "23 July 2024",
        comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
        avatarText: "T",
        avatarColor: "#E8F5E8",
        avatarTextColor: "#4CAF50",
        replyCount: 5,
      ),
      Comment(
        id: "2",
        name: "Ikkon Architects",
        date: "23 July 2024",
        comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
        avatarText: "I",
        avatarColor: "#FFF3CD",
        avatarTextColor: "#FF9800",
        replyCount: 52,
      ),
      Comment(
        id: "3",
        name: "Red Architects",
        date: "23 July 2024",
        comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
        avatarText: "R",
        avatarColor: "#E3F2FD",
        avatarTextColor: "#2196F3",
        replyCount: 0,
      ),
    ];
  }
}

class ProjectElement {
  final String id;
  final String name;
  final String category;
  final String brand;
  final String collection;
  final String material;
  final String price;
  final String description;
  final List<String> images;
  final double x; // Position on main image (0-1)
  final double y; // Position on main image (0-1)

  ProjectElement({
    required this.id,
    required this.name,
    required this.category,
    required this.brand,
    required this.collection,
    required this.material,
    required this.price,
    required this.description,
    required this.images,
    required this.x,
    required this.y,
  });

  static List<ProjectElement> sampleElements() {
    return [
      ProjectElement(
        id: "1",
        name: "Sofa",
        category: "Sofa",
        brand: "BoConcept",
        collection: "Bergamo",
        material: "Fabric > Grey Slate + Oak + Metal",
        price: "Rs. 2,00,000 +",
        description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus",
        images: [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
          'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80',
          'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80',
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
          'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80',
        ],
        x: 0.7,
        y: 0.6,
      ),
      ProjectElement(
        id: "2",
        name: "Chair",
        category: "Chair",
        brand: "BoConcept",
        collection: "Bergamo",
        material: "Wood + Fabric",
        price: "Rs. 50,000 +",
        description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        images: [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
          'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80',
        ],
        x: 0.3,
        y: 0.4,
      ),
      ProjectElement(
        id: "3",
        name: "Center table",
        category: "Center table",
        brand: "BoConcept",
        collection: "Bergamo",
        material: "Oak + Metal",
        price: "Rs. 75,000 +",
        description: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
        images: [
          'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80',
        ],
        x: 0.5,
        y: 0.7,
      ),
    ];
  }
}
