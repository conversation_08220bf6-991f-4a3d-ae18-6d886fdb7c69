import 'package:get/get.dart';
import 'package:flutter/material.dart';

class NavigationService extends GetxService {
  static NavigationService get to => Get.find();
  
  final RxString currentRoute = '/'.obs;
  final RxMap<String, dynamic> currentArguments = <String, dynamic>{}.obs;
  final RxList<NavigationState> navigationHistory = <NavigationState>[].obs;
  final RxInt currentBottomNavIndex = 0.obs;
  
  // Maximum history length to prevent memory issues
  static const int maxHistoryLength = 10;

  @override
  void onInit() {
    super.onInit();
    _setupRouteObserver();
  }

  void _setupRouteObserver() {
    // Listen to route changes
    ever(currentRoute, (String route) {
      _addToHistory(route, currentArguments);
    });
  }

  void updateCurrentRoute(String route, {Map<String, dynamic>? arguments}) {
    currentRoute.value = route;
    if (arguments != null) {
      currentArguments.value = arguments;
    } else {
      currentArguments.clear();
    }
  }

  void updateBottomNavIndex(int index) {
    currentBottomNavIndex.value = index;
  }

  void _addToHistory(String route, RxMap<String, dynamic> arguments) {
    final navigationState = NavigationState(
      route: route,
      arguments: Map<String, dynamic>.from(arguments),
      timestamp: DateTime.now(),
      bottomNavIndex: currentBottomNavIndex.value,
    );

    navigationHistory.add(navigationState);

    // Keep history within limits
    if (navigationHistory.length > maxHistoryLength) {
      navigationHistory.removeAt(0);
    }
  }

  NavigationState? getLastValidRoute() {
    // Get the last route that's not the no-internet screen
    for (int i = navigationHistory.length - 1; i >= 0; i--) {
      final state = navigationHistory[i];
      if (state.route != '/no-internet' && 
          state.route != '/' && 
          state.route.isNotEmpty) {
        return state;
      }
    }
    return null;
  }

  void restoreLastRoute() {
    final lastState = getLastValidRoute();
    if (lastState != null) {
      if (lastState.arguments.isNotEmpty) {
        Get.offAllNamed(
          lastState.route, 
          arguments: lastState.arguments,
        );
      } else {
        Get.offAllNamed(lastState.route);
      }
      
      // Restore bottom navigation index if applicable
      if (lastState.route == '/bottomNavigationScreen') {
        currentBottomNavIndex.value = lastState.bottomNavIndex;
      }
    } else {
      // Fallback to home
      Get.offAllNamed('/bottomNavigationScreen', arguments: {'index': 1});
    }
  }

  void navigateToRoute(String route, {Map<String, dynamic>? arguments}) {
    updateCurrentRoute(route, arguments: arguments);
    
    if (arguments != null) {
      Get.toNamed(route, arguments: arguments);
    } else {
      Get.toNamed(route);
    }
  }

  void navigateAndReplace(String route, {Map<String, dynamic>? arguments}) {
    updateCurrentRoute(route, arguments: arguments);
    
    if (arguments != null) {
      Get.offNamed(route, arguments: arguments);
    } else {
      Get.offNamed(route);
    }
  }

  void navigateAndClearStack(String route, {Map<String, dynamic>? arguments}) {
    updateCurrentRoute(route, arguments: arguments);
    
    if (arguments != null) {
      Get.offAllNamed(route, arguments: arguments);
    } else {
      Get.offAllNamed(route);
    }
  }

  bool canGoBack() {
    return navigationHistory.length > 1;
  }

  void goBack() {
    if (canGoBack()) {
      // Remove current route
      if (navigationHistory.isNotEmpty) {
        navigationHistory.removeLast();
      }
      
      // Get previous route
      final previousState = navigationHistory.isNotEmpty 
          ? navigationHistory.last 
          : null;
      
      if (previousState != null) {
        updateCurrentRoute(previousState.route, arguments: previousState.arguments);
        Get.back();
      }
    }
  }

  void clearHistory() {
    navigationHistory.clear();
  }

  // Get navigation statistics for debugging
  Map<String, dynamic> getNavigationStats() {
    return {
      'currentRoute': currentRoute.value,
      'historyLength': navigationHistory.length,
      'currentBottomNavIndex': currentBottomNavIndex.value,
      'canGoBack': canGoBack(),
      'lastValidRoute': getLastValidRoute()?.route ?? 'None',
    };
  }

  // Save navigation state to persistent storage (for app restart scenarios)
  Map<String, dynamic> saveState() {
    return {
      'currentRoute': currentRoute.value,
      'currentArguments': currentArguments,
      'currentBottomNavIndex': currentBottomNavIndex.value,
      'lastValidRoute': getLastValidRoute()?.toJson(),
    };
  }

  // Restore navigation state from persistent storage
  void restoreState(Map<String, dynamic> state) {
    try {
      currentRoute.value = state['currentRoute'] ?? '/';
      currentArguments.value = Map<String, dynamic>.from(state['currentArguments'] ?? {});
      currentBottomNavIndex.value = state['currentBottomNavIndex'] ?? 0;
      
      if (state['lastValidRoute'] != null) {
        final lastValidRoute = NavigationState.fromJson(state['lastValidRoute']);
        navigationHistory.add(lastValidRoute);
      }
    } catch (e) {
      print('Error restoring navigation state: $e');
    }
  }
}

class NavigationState {
  final String route;
  final Map<String, dynamic> arguments;
  final DateTime timestamp;
  final int bottomNavIndex;

  NavigationState({
    required this.route,
    required this.arguments,
    required this.timestamp,
    this.bottomNavIndex = 0,
  });

  Map<String, dynamic> toJson() {
    return {
      'route': route,
      'arguments': arguments,
      'timestamp': timestamp.toIso8601String(),
      'bottomNavIndex': bottomNavIndex,
    };
  }

  factory NavigationState.fromJson(Map<String, dynamic> json) {
    return NavigationState(
      route: json['route'] ?? '/',
      arguments: Map<String, dynamic>.from(json['arguments'] ?? {}),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      bottomNavIndex: json['bottomNavIndex'] ?? 0,
    );
  }

  @override
  String toString() {
    return 'NavigationState(route: $route, arguments: $arguments, timestamp: $timestamp, bottomNavIndex: $bottomNavIndex)';
  }
}
