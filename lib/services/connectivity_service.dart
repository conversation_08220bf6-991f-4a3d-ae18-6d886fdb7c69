import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'navigation_service.dart';

class ConnectivityService extends GetxService {
  static ConnectivityService get to => Get.find();

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<List<ConnectivityResult>> _connectivitySubscription;

  final RxBool isConnected = true.obs;
  final RxBool wasDisconnected = false.obs;
  final RxString lastRoute = '/'.obs;
  final RxMap<String, dynamic> lastArguments = <String, dynamic>{}.obs;
  final RxBool isInitialized = false.obs;
  
  @override
  void onInit() {
    super.onInit();
    _initConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  Future<void> _initConnectivity() async {
    try {
      final List<ConnectivityResult> results = await _connectivity.checkConnectivity();
      final bool hasNetworkConnection = results.any((result) =>
        result == ConnectivityResult.mobile ||
        result == ConnectivityResult.wifi ||
        result == ConnectivityResult.ethernet
      );

      if (hasNetworkConnection) {
        // Test actual internet connectivity
        await _testInternetConnectivity();
      } else {
        isConnected.value = false;
      }

      isInitialized.value = true;
    } catch (e) {
      // Handle error silently in production
      isConnected.value = false;
    }
  }

  void _updateConnectionStatus(List<ConnectivityResult> results) {
    // Only process connectivity changes after initialization
    if (!isInitialized.value) return;

    final bool hasConnection = results.any((result) =>
      result == ConnectivityResult.mobile ||
      result == ConnectivityResult.wifi ||
      result == ConnectivityResult.ethernet
    );

    // Only show no internet screen if we actually lost connection
    // Don't trigger on image loading errors or other network issues
    if (isConnected.value && !hasConnection) {
      // Connection lost - save current route
      wasDisconnected.value = true;
      _saveCurrentRoute();
      _showNoInternetScreen();
    } else if (!isConnected.value && hasConnection && wasDisconnected.value) {
      // Connection restored - only if we were previously disconnected
      _handleConnectionRestored();
    }

    isConnected.value = hasConnection;
  }

  void _saveCurrentRoute() {
    try {
      final navigationService = NavigationService.to;
      final currentRoute = navigationService.currentRoute.value;
      final currentArguments = navigationService.currentArguments;

      if (currentRoute != '/no-internet') {
        lastRoute.value = currentRoute;
        lastArguments.value = Map<String, dynamic>.from(currentArguments);
      }
    } catch (e) {
      // Handle error silently in production
      lastRoute.value = '/bottomNavigationScreen';
      lastArguments.clear();
    }
  }

  void _showNoInternetScreen() {
    final currentRoute = Get.currentRoute;
    // Don't show no internet screen during app initialization or if already showing
    if (currentRoute != '/no-internet' &&
        currentRoute != '/' &&
        isInitialized.value) {
      Get.offAllNamed('/no-internet');
    }
  }

  void _handleConnectionRestored() {
    if (wasDisconnected.value && Get.currentRoute == '/no-internet') {
      wasDisconnected.value = false;

      try {
        final navigationService = NavigationService.to;
        navigationService.restoreLastRoute();
      } catch (e) {
        // Fallback navigation
        Get.offAllNamed('/bottomNavigationScreen', arguments: {'index': 1});
      }
    }
  }

  Future<void> retryConnection() async {
    await _testInternetConnectivity();
  }

  Future<void> _testInternetConnectivity() async {
    try {
      // Test actual internet connectivity with a simple HTTP request
      final response = await http.get(
        Uri.parse('https://www.google.com'),
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(const Duration(seconds: 5));

      final bool hasInternet = response.statusCode == 200;

      if (!isConnected.value && hasInternet) {
        // Connection restored
        isConnected.value = true;
        _handleConnectionRestored();
      } else if (isConnected.value && !hasInternet) {
        // Connection lost
        isConnected.value = false;
        wasDisconnected.value = true;
        _saveCurrentRoute();
        _showNoInternetScreen();
      } else {
        isConnected.value = hasInternet;
      }
    } catch (e) {
      // If we can't reach the internet, mark as disconnected
      if (isConnected.value) {
        isConnected.value = false;
        wasDisconnected.value = true;
        _saveCurrentRoute();
        _showNoInternetScreen();
      }
    }
  }

  bool get hasConnection => isConnected.value;
}
