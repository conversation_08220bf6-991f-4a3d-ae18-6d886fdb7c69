import 'package:flutter/material.dart';
import 'package:get/get.dart';

enum DeviceType { mobile, tablet, desktop }

class ResponsiveService extends GetxService {
  static ResponsiveService get to => Get.find();
  
  final Rx<DeviceType> deviceType = DeviceType.mobile.obs;
  final RxDouble screenWidth = 0.0.obs;
  final RxDouble screenHeight = 0.0.obs;
  final RxBool isPortrait = true.obs;
  
  // Breakpoints
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 1024;
  


  void updateScreenInfo() {
    try {
      final size = Get.size;
      if (size.width > 0 && size.height > 0) {
        screenWidth.value = size.width;
        screenHeight.value = size.height;
        isPortrait.value = size.height > size.width;

        // Determine device type
        _updateDeviceType();
      }
    } catch (e) {
      // Fallback values for when Get.size is not available
      screenWidth.value = 375.0; // Default mobile width
      screenHeight.value = 667.0; // Default mobile height
      isPortrait.value = true;
      deviceType.value = DeviceType.mobile;
    }
  }

  void _updateDeviceType() {
    // Determine device type
    if (screenWidth.value < mobileBreakpoint) {
      deviceType.value = DeviceType.mobile;
    } else if (screenWidth.value < tabletBreakpoint) {
      deviceType.value = DeviceType.tablet;
    } else {
      deviceType.value = DeviceType.desktop;
    }
  }

  // Responsive values
  double responsiveValue({
    required double mobile,
    double? tablet,
    double? desktop,
  }) {
    switch (deviceType.value) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile * 1.2;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile * 1.5;
    }
  }

  // Responsive padding
  EdgeInsets responsivePadding({
    required EdgeInsets mobile,
    EdgeInsets? tablet,
    EdgeInsets? desktop,
  }) {
    switch (deviceType.value) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile * 1.2;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile * 1.5;
    }
  }

  // Responsive font size
  double responsiveFontSize(double baseFontSize) {
    return responsiveValue(
      mobile: baseFontSize,
      tablet: baseFontSize * 1.1,
      desktop: baseFontSize * 1.2,
    );
  }

  // Grid columns based on device type
  int getGridColumns({int mobile = 2, int tablet = 3, int desktop = 4}) {
    switch (deviceType.value) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet;
      case DeviceType.desktop:
        return desktop;
    }
  }

  // Check if device is mobile
  bool get isMobile => deviceType.value == DeviceType.mobile;
  
  // Check if device is tablet
  bool get isTablet => deviceType.value == DeviceType.tablet;
  
  // Check if device is desktop
  bool get isDesktop => deviceType.value == DeviceType.desktop;
  
  // Get responsive width percentage
  double widthPercent(double percent) {
    return screenWidth.value * (percent / 100);
  }
  
  // Get responsive height percentage
  double heightPercent(double percent) {
    return screenHeight.value * (percent / 100);
  }

  // Responsive margin
  EdgeInsets responsiveMargin({
    double mobile = 16.0,
    double? tablet,
    double? desktop,
  }) {
    final value = responsiveValue(
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
    );
    return EdgeInsets.all(value);
  }

  // Responsive card width
  double getCardWidth() {
    switch (deviceType.value) {
      case DeviceType.mobile:
        return screenWidth.value * 0.9;
      case DeviceType.tablet:
        return screenWidth.value * 0.7;
      case DeviceType.desktop:
        return screenWidth.value * 0.5;
    }
  }

  // Responsive bottom navigation height
  double getBottomNavHeight() {
    return responsiveValue(
      mobile: 70.0,
      tablet: 80.0,
      desktop: 90.0,
    );
  }
}
