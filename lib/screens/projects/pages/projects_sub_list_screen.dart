import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../constants/assets.dart';
import '../../../utils/utils.dart';
import '../../bottom_navigation/controller/bottom_navigation_controller.dart';
import '../controller/projects_sub_list_controller.dart';
import '../widgets/floating_tab_bar_toggle_widget.dart';
import '../widgets/projects_category_button_widget.dart';

class ProjectsSubListScreen extends StatefulWidget {
  @override
  State<ProjectsSubListScreen> createState() => _ProjectsSubListScreenState();
}

class _ProjectsSubListScreenState extends State<ProjectsSubListScreen> {
  final ProjectsSubListController controller = Get.put(ProjectsSubListController());
  final ScrollController _scrollController = ScrollController();
  final RxInt selectedIndex = 0.obs;
  final List<String> tabs = ['For You', 'Latest', 'Popular'];

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<bool> _onWillPop() async {
    final navController = Get.find<BottomNavigationController>();
    final currentTabIndex = navController.selectedIndex.value;
    if (navController.navigatorKeys[currentTabIndex].currentState?.canPop() ?? false) {
      navController.navigatorKeys[currentTabIndex].currentState?.pop();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.blue.shade50,
        body: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverAppBar(
              leading: IconButton(
                onPressed: () => Navigator.pop(context),
                icon: const Icon(Icons.arrow_back_ios),
              ),
              title: const Text("Residential"),
              expandedHeight: 340,
              pinned: true,
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  children: [
                    Positioned.fill(
                      child: Utils.pngImage(AssetStrings.projectsBgImage),
                    ),
                    Positioned.fill(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                        child: Container(color: Colors.black.withOpacity(0.0)),
                      ),
                    ),
                    Positioned(
                      top: 150,
                      left: 20,
                      child: const Text(
                        "Residential",
                        style: TextStyle(
                          fontSize: 50,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [Shadow(blurRadius: 10, color: Colors.black26)],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(90),
                child: _buildCategoryButtons(),
              ),
            ),

            // Grid content
            Obx(() {
              return SliverPadding(
                padding: const EdgeInsets.all(4),
                sliver: SliverGrid(
                  delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                      return Stack(
                        children: [
                          Positioned.fill(
                            child: Image.network(
                              controller.postsImages[index],
                              fit: BoxFit.cover,
                            ),
                          ),
                          const Positioned(
                            top: 5,
                            right: 5,
                            child: Icon(Icons.browse_gallery_outlined),
                          ),
                        ],
                      );
                    },
                    childCount: controller.postsImages.length,
                  ),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: controller.crossAxisCount.value,
                    mainAxisSpacing: 4,
                    crossAxisSpacing: 4,
                  ),
                ),
              );
            }),
          ],
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: FloatingTabBarWithToggle(
          tabs: tabs,
          selectedIndex: selectedIndex,
          crossAxisCount: controller.crossAxisCount,
          getGridIcon: controller.getGridIcon,
        ),
      ),
    );
  }

  Widget _buildCategoryButtons() {
    final categories = controller.categories;
    const itemsPerRow = 6;
    final rowCount = (categories.length / itemsPerRow).ceil();

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: List.generate(rowCount, (rowIndex) {
          final startIndex = rowIndex * itemsPerRow;
          final endIndex = min(startIndex + itemsPerRow, categories.length);
          final rowItems = categories.sublist(startIndex, endIndex);

          return Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              children: rowItems.map((category) {
                return ProjectCategoryButton(
                  category: category,
                  showColorBox: false,
                  fontSize: 10,
                  color: Colors.black,
                  borderColor: Colors.black,
                  onTap: () => Get.toNamed('/projectsSubItemScreen', id: 2),
                );
              }).toList(),
            ),
          );
        }),
      ),
    );
  }
}
