
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../constants/assets.dart';
import '../../../utils/utils.dart';
import '../../home/<USER>/category_button_widget.dart';
import '../controller/projects_sub_item_controller.dart';
import '../widgets/floating_tab_bar_toggle_widget.dart';

class ProjectsSubItemScreen extends StatefulWidget {
  const ProjectsSubItemScreen({super.key});

  @override
  State<ProjectsSubItemScreen> createState() => _ProjectsSubItemScreenState();
}

class _ProjectsSubItemScreenState extends State<ProjectsSubItemScreen> {
  final ProjectsSubItemController controller = Get.put(ProjectsSubItemController());
  final ScrollController _scrollController = ScrollController();
  final RxInt selectedTabIndex = 0.obs;
  final RxInt selectedCategoryIndex = (-1).obs;

  final List<String> tabs = ['For You', 'Latest', 'Popular'];

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _handleCategoryTap(int index) {
    selectedCategoryIndex.value =
    (selectedCategoryIndex.value == index) ? -1 : index;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.blue.shade50,
      body: Obx(() {
        final bool isCategoryExpanded = selectedCategoryIndex.value != -1;
        final double headerHeight = isCategoryExpanded ? 700 : 340;

        return CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                height: headerHeight,
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Background image with blur
                    _buildBackground(),

                    // Back button
                    Positioned(
                      top: 40,
                      left: 10,
                      child: IconButton( 
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
                      ),
                    ),

                    // Title
                    const Positioned(
                      top: 150,
                      left: 20,
                      child: Text(
                        "Bedroom",
                        style: TextStyle(
                          fontSize: 80,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [Shadow(blurRadius: 10, color: Colors.black26)],
                        ),
                      ),
                    ),

                    // Categories section
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        color: Colors.black.withOpacity(0.3),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Column(
                          children: [
                            // Categories horizontal list
                            _buildCategoryList(),

                            // Expanded content for selected category
                            if (isCategoryExpanded)
                              _buildExpandedCategoryContent(),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Grid content
            _buildImageGrid(),
          ],
        );
      }),
      floatingActionButton: FloatingTabBarWithToggle(
        tabs: tabs,
        selectedIndex: selectedTabIndex,
        crossAxisCount: controller.crossAxisCount,
        getGridIcon: controller.getGridIcon,
      ),
    );
  }

  Widget _buildBackground() {
    return Stack(
      children: [
        Positioned.fill(
          child:  Utils.pngImage(AssetStrings.projectsBgImage),
        ),
        Positioned.fill(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
            child: Container(color: Colors.black.withOpacity(0.0)),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryList() {
    return SizedBox(
      height: 60,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 10),
        itemCount: controller.categories.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: CategoryButton(
              label: controller.categories[index],
              highlight: selectedCategoryIndex.value == index,
              onTap: () => _handleCategoryTap(index),
            ),
          );
        },
      ),
    );
  }

  Widget _buildExpandedCategoryContent() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            controller.categories[selectedCategoryIndex.value],
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.orange[200],
            ),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 480,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    "1. Content for selected category...",
                    style: TextStyle(color: Colors.white70),
                  ),
                  SizedBox(height: 8),
                  Text(
                    "2. More details about this category...",
                    style: TextStyle(color: Colors.white70),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(4),
      sliver: SliverGrid(
        delegate: SliverChildBuilderDelegate(
              (context, index) {
            return Stack(
              children: [
                Positioned.fill(
                  child: Image.network(
                    controller.postsImages[index],
                    fit: BoxFit.cover,
                  ),
                ),
                const Positioned(
                  top: 5,
                  right: 5,
                  child: Icon(Icons.browse_gallery_outlined),
                ),
              ],
            );
          },
          childCount: controller.postsImages.length,
        ),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: controller.crossAxisCount.value,
          mainAxisSpacing: 4,
          crossAxisSpacing: 4,
        ),
      ),
    );
  }
}