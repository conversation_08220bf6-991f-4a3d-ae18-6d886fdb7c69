import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/widgets/cached_image_network_widget.dart';
import '../../../constants/colors.dart';
import '../../../models/project_model.dart';
import '../controller/projects_list_controller.dart';

class ProjectDetailController extends GetxController with GetTickerProviderStateMixin {
  var isFavorite = false.obs;
  var selectedTabIndex = 0.obs; // 0 = Info, 1 = Elements
  var currentImageIndex = 0.obs; // For carousel
  late TabController innerTabController;
  TabController? elementsTabController;
  final List<String> innerTabs = ["About", "Team", "Comments"];
  final List<String> elementCategories = ["Sofa", "Chair", "Wall Art", "Center table", "Paint"];

  // Project data - will be set from arguments
  late ProjectModel project;

  // Elements section
  var selectedElement = Rx<ProjectElement?>(null);
  var selectedCategoryIndex = 0.obs;

  // Main sections
  final List<String> mainTabs = ["Info", "Elements"];

  // Computed properties based on project data
  List<String> get carouselImages => project.images;
  List<String> get images => project.images;

  @override
  void onInit() {
    super.onInit();

    // Get project data from ProjectListController or use default
    try {
      final projectListController = Get.find<ProjectListController>();
      project = projectListController.selectedProject.value ?? ProjectModel.sample();
    } catch (e) {
      // If controller not found, use default project
      project = ProjectModel.sample();
    }
    isFavorite.value = project.isFavorite;

    innerTabController = TabController(length: innerTabs.length, vsync: this);
    elementsTabController = TabController(length: elementCategories.length, vsync: this);
    _preloadImages();
  }

  // Preload images for better performance
  void _preloadImages() {
    for (String imageUrl in project.images) {
      // This will cache the images in advance
      precacheImage(NetworkImage(imageUrl), Get.context!);
    }
  }

  void toggleFavorite() {
    isFavorite.value = !isFavorite.value;
  }

  void changeTab(int index) {
    selectedTabIndex.value = index;
  }

  void onPageChanged(int index) {
    currentImageIndex.value = index;
  }

  void showElements() {
    selectedTabIndex.value = 1; // Switch to Elements tab
  }

  void hideElements() {
    selectedTabIndex.value = 0; // Switch back to Info tab
    selectedElement.value = null;
  }

  void selectElement(ProjectElement element) {
    selectedElement.value = element;
  }

  // Add a method to update the selected tab based on the carousel index
  void updateSelectedTabBasedOnCarousel(int index) {
    if (index < mainTabs.length) {
      selectedTabIndex.value = index;
    }
  }

  List<ProjectElement> getElementsForCategory(String category) {
    return project.elements.where((element) => element.category == category).toList();
  }

  void onCategoryTabChanged(int index) {
    selectedCategoryIndex.value = index;
  }

  @override
  void onClose() {
    innerTabController.dispose();
    elementsTabController?.dispose();
    super.onClose();
  }
}

class ProjectDetailScreen extends StatelessWidget {
  const ProjectDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProjectDetailController());
    return Scaffold(
      backgroundColor: Colors.black,
      body: Obx(() {
        // Wait for controller initialization
        if (controller.elementsTabController == null) {
          return Center(child: CircularProgressIndicator(color: Colors.white));
        }
        return controller.selectedTabIndex.value == 0
            ? _buildInfoView(controller, context)
            : _buildElementsView(controller);
      }),
    );
  }

  Widget _buildInfoView(ProjectDetailController controller, BuildContext context) {
    return Column(
      children: [
        // Fixed height carousel container with overlays
        Container(
          height: 400, // Fixed height for the image container
          child: Stack(
            children: [
              // Main carousel with fixed container
              Positioned.fill(
                child: PageView.builder(
                  onPageChanged: controller.onPageChanged,
                  itemCount: controller.carouselImages.length,
                  itemBuilder: (context, index) {
                    return Image.network(
                      controller.carouselImages[index],
                      fit: BoxFit.cover,
                      loadingBuilder: (context, child, loadingProgress) {
                        if (loadingProgress == null) return child;
                        return Container(
                          color: Colors.grey[300],
                          child: Center(
                            child: CircularProgressIndicator(
                              color: Colors.white,
                            ),
                          ),
                        );
                      },
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.error,
                                  color: Colors.red,
                                  size: 50,
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Failed to load image',
                                  style: TextStyle(color: Colors.red),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),

                // Top overlay with back button
                Positioned(
                  top: MediaQuery.of(context).padding.top + 10,
                  left: 16,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: IconButton(
                      icon: Icon(Icons.arrow_back_ios, color: Colors.white, size: 20),
                      onPressed: () => Get.back(),
                    ),
                  ),
                ),

                // Carousel indicators
                Positioned(
                  bottom: 120,
                  left: 0,
                  right: 0,
                  child: Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      controller.carouselImages.length,
                      (index) => Container(
                        margin: EdgeInsets.symmetric(horizontal: 4),
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: controller.currentImageIndex.value == index
                              ? Colors.white
                              : Colors.white.withValues(alpha: 0.4),
                        ),
                      ),
                    ),
                  )),
                ),

                // Bottom left overlay with "Elements" text
                Positioned(
                  bottom: 20,
                  left: 20,
                  child: GestureDetector(
                    onTap: () => controller.showElements(),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: Text(
                        "Elements",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),



              ],
            ),
          ),

          // Black section with studio info below image
          Container(
            color: Colors.black,
            padding: EdgeInsets.all(16),
            child: Row(
              children: [
                // Studio avatar and info
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(Icons.business, color: Colors.black, size: 16),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        "MuseLAB",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        "Open House",
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
                // Trophy icon
                Container(
                  margin: EdgeInsets.only(right: 12),
                  child: Icon(
                    Icons.emoji_events,
                    color: Colors.yellow,
                    size: 24,
                  ),
                ),
                // Star icon
                Obx(() => GestureDetector(
                  onTap: controller.toggleFavorite,
                  child: Icon(
                    controller.isFavorite.value ? Icons.star : Icons.star_border,
                    color: Colors.yellow,
                    size: 24,
                  ),
                )),
              ],
            ),
          ),

          // Bottom section with tabs and content
          Expanded(
            child: Container(
              color: Colors.white,
              child: Column(
                children: [
                  // Tab bar - More compact design
                  Container(
                    color: ColorConstants.appColor,
                    child: TabBar(
                      controller: controller.innerTabController,
                      indicatorColor: Colors.black,
                      indicatorWeight: 2,
                      indicatorSize: TabBarIndicatorSize.tab,
                      labelColor: Colors.black,
                      unselectedLabelColor: Colors.grey,
                      labelStyle: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontSize: 13,
                      ),
                      unselectedLabelStyle: const TextStyle(
                        fontWeight: FontWeight.normal,
                        fontSize: 13,
                      ),
                      padding: EdgeInsets.zero,
                      labelPadding: const EdgeInsets.symmetric(vertical: 6),
                      tabs: const [
                        Tab(text: "About"),
                        Tab(text: "Team"),
                        Tab(text: "Comments -12"),
                      ],
                    ),
                  ),

                  // Tab content
                  Expanded(
                    child: TabBarView(
                      controller: controller.innerTabController,
                      children: [
                        _buildAboutSection(controller),
                        _buildTeamSection(controller),
                        _buildCommentsSection(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      );
  }

  Widget _buildElementsView(ProjectDetailController controller) {
    return Stack(
      children: [
        Column(
          children: [
            // Top image section with elements
            Container(
              height: 400,
              child: Stack(
                children: [
                  // Main image
                  Positioned.fill(
                    child: PageView.builder(
                      onPageChanged: controller.onPageChanged,
                      itemCount: controller.carouselImages.length,
                      itemBuilder: (context, index) {
                        return Image.network(
                          controller.carouselImages[index],
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: Colors.grey[300],
                              child: Center(
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: Colors.grey[300],
                              child: Center(
                                child: Icon(
                                  Icons.error,
                                  color: Colors.red,
                                  size: 50,
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
                  ),

                  // Element dots overlay
                  ...controller.project.elements.map((element) =>
                    Positioned(
                      left: element.x * 400 - 12,
                      top: element.y * 400 - 12,
                      child: GestureDetector(
                        onTap: () => controller.selectElement(element),
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.black, width: 2),
                          ),
                          child: Center(
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: Colors.black,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ).toList(),

                  // Back button
                  Positioned(
                    top: 50,
                    left: 16,
                    child: GestureDetector(
                      onTap: () => Get.back(),
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                  ),

                  // Project info overlay
                  Positioned(
                    bottom: 16,
                    left: 16,
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.business,
                            color: Colors.black,
                            size: 20,
                          ),
                        ),
                        SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              controller.project.studioName,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              controller.project.projectName,
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Trophy and star icons
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: Row(
                      children: [
                        Icon(
                          Icons.emoji_events,
                          color: Colors.yellow,
                          size: 24,
                        ),
                        SizedBox(width: 8),
                        Icon(
                          Icons.star,
                          color: Colors.yellow,
                          size: 24,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Bottom section with element categories
            Expanded(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // Category TabBar
                    if (controller.elementsTabController != null) ...[
                      Container(
                        color: ColorConstants.appColor,
                        child: TabBar(
                          controller: controller.elementsTabController!,
                          onTap: controller.onCategoryTabChanged,
                          isScrollable: true,
                          indicatorColor: Colors.black,
                          indicatorWeight: 2,
                          labelColor: Colors.white,
                          unselectedLabelColor: Colors.black,
                          labelStyle: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
                          unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal, fontSize: 14),
                          indicator: BoxDecoration(
                            color: Colors.black,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          labelPadding: EdgeInsets.symmetric(horizontal: 16),
                          tabs: controller.elementCategories.map((category) => 
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Tab(text: category),
                            ),
                          ).toList(),
                        ),
                      ),

                      // TabBarView for category content
                      Expanded(
                        child: TabBarView(
                          controller: controller.elementsTabController!,
                          children: controller.elementCategories.map((category) => 
                            _buildCategoryContent(controller, category)
                          ).toList(),
                        ),
                      ),
                    ] else
                      Expanded(
                        child: Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCategoryContent(ProjectDetailController controller, String category) {
    final categoryElements = controller.getElementsForCategory(category);
    
    if (categoryElements.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.category_outlined, size: 64, color: Colors.grey[400]),
            SizedBox(height: 16),
            Text(
              "No $category items found",
              style: TextStyle(color: Colors.grey[600], fontSize: 16),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: categoryElements.length,
      itemBuilder: (context, index) {
        final element = categoryElements[index];
        return GestureDetector(
          onTap: () => controller.selectElement(element),
          child: Container(
            margin: EdgeInsets.only(bottom: 16),
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[300]!),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Element image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[200],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      element.images.first,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: Icon(Icons.image, color: Colors.grey[500]),
                        );
                      },
                    ),
                  ),
                ),
                SizedBox(width: 16),
                // Element details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        element.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        element.brand,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        element.price,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildElementDetails(ProjectElement element) {
    return Container(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Element title and actions
          Row(
            children: [
              Expanded(
                child: Text(
                  element.name,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
              ),
              Icon(Icons.favorite_border, color: Colors.grey),
              SizedBox(width: 16),
              Icon(Icons.menu, color: Colors.grey),
            ],
          ),

          SizedBox(height: 16),

          // Element details
          _buildDetailRow("Brand", element.brand),
          _buildDetailRow("Collection", element.collection),
          _buildDetailRow("Material", element.material),
          _buildDetailRow("Price", element.price),

          SizedBox(height: 20),

          // Description
          Text(
            "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus",
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
              height: 1.5,
            ),
          ),

          SizedBox(height: 20),

          // Similar images
          Container(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: element.images.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 100,
                  height: 100,
                  margin: EdgeInsets.only(right: 12),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: Colors.grey[200],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Image.network(
                      element.images[index],
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey[300],
                          child: Icon(
                            Icons.image,
                            color: Colors.grey[500],
                          ),
                        );
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Text(
            ": $value",
            style: TextStyle(
              color: Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultElementView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.touch_app,
            size: 64,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            "Tap on any element in the image above",
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
          Text(
            "to see its details",
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommentsSection() {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        _buildCommentItem(
          name: "Tantra Architects",
          date: "23 July 2024",
          comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
          avatarColor: const Color(0xFFE8F5E8),
          avatarText: "T",
          avatarTextColor: const Color(0xFF4CAF50),
          replyCount: 5,
        ),
        const SizedBox(height: 16),
        _buildCommentItem(
          name: "Ikkon Architects",
          date: "23 July 2024",
          comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
          avatarColor: const Color(0xFFFFF3CD),
          avatarText: "I",
          avatarTextColor: const Color(0xFFFF9800),
          replyCount: 52,
        ),
        const SizedBox(height: 16),
        _buildCommentItem(
          name: "Red Architects",
          date: "23 July 2024",
          comment: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce convallis pellentesque metus id lacinia. Nunc dapibus pulvinar auctor.",
          avatarColor: const Color(0xFFE3F2FD),
          avatarText: "R",
          avatarTextColor: const Color(0xFF2196F3),
          replyCount: 0,
        ),
      ],
    );
  }

  Widget _buildCommentItem({
    required String name,
    required String date,
    required String comment,
    required Color avatarColor,
    required String avatarText,
    required Color avatarTextColor,
    required int replyCount,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Avatar
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: avatarColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  avatarText,
                  style: TextStyle(
                    color: avatarTextColor,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            // Comment content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name and date
                  Row(
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black,
                        ),
                      ),
                      const Spacer(),
                      Text(
                        date,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Comment text
                  Text(
                    comment,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.grey,
                      height: 1.4,
                    ),
                  ),
                  const SizedBox(height: 8),
                  // Expand arrow and reply count
                  Row(
                    children: [
                      const Spacer(),
                      if (replyCount > 0) ...[
                        Text(
                          "$replyCount Replies",
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      const Icon(
                        Icons.keyboard_arrow_down,
                        color: Colors.grey,
                        size: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAboutSection(ProjectDetailController controller) {
    return Stack(
      children: [
        // Scrollable content area
        SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 85.0), // Bottom padding for fixed image scroller
          child: Column(
            children: [
              // Project details with heart and menu icons - More compact
              Row(
                children: [
                  Expanded(
                    child: Column(
                      children: [
                        _buildInfoRow("Category", controller.project.category),
                        _buildInfoRow("Project Name", controller.project.projectName),
                        _buildInfoRow("Location", controller.project.location),
                        _buildInfoRow("Year", controller.project.year),
                        _buildInfoRow("Duration", controller.project.duration),
                        _buildInfoRow("Area", controller.project.area),
                        _buildInfoRow("Cost", controller.project.cost),
                      ],
                    ),
                  ),
                  Column(
                    children: [
                      IconButton(
                        onPressed: () {},
                        icon: Icon(Icons.favorite_border, color: Colors.grey, size: 22),
                        padding: EdgeInsets.all(4),
                        constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                      SizedBox(height: 4),
                      IconButton(
                        onPressed: () {},
                        icon: Icon(Icons.more_horiz, color: Colors.grey, size: 22),
                        padding: EdgeInsets.all(4),
                        constraints: BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Description text - More compact
              Text(
                controller.project.description,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.black87,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),

        // Fixed image scroller at bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 75,
            padding: const EdgeInsets.fromLTRB(12.0, 4.0, 12.0, 8.0),
            color: Colors.white,
            child: _buildImageScroller(),
          ),
        ),
      ],
    );
  }


  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                color: Colors.black87,
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          Text(
            " : ",
            style: TextStyle(
              fontSize: 13,
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 13,
                color: Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageScroller() {
    final images = [
      'https://unsplash.com/photos/urH155LONWs/download?force=true',
      'https://unsplash.com/photos/8Tj2HrEWZxc/download?force=true',
      'https://unsplash.com/photos/38BThc6Q-ZI/download?force=true',
      'https://unsplash.com/photos/4HG3Ca3EzWw/download?force=true',
      'https://unsplash.com/photos/urH155LONWs/download?force=true',
      'https://unsplash.com/photos/8Tj2HrEWZxc/download?force=true',
    ];

    return SizedBox(
      height: 55,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 4),
        itemCount: images.length,
        separatorBuilder: (_, __) => const SizedBox(width: 6),
        itemBuilder: (context, index) => Container(
          width: 55,
          height: 55,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 2,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: CachedImageWidget(
              imageUrl: images[index],
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTeamSection(ProjectDetailController controller) {
    // Convert team members to map for display
    final teamData = <String, String>{};
    for (var member in controller.project.teamMembers) {
      teamData[member.role] = member.name;
    }

    return Stack(
      children: [
        // Scrollable team list
        ListView.separated(
          padding: const EdgeInsets.fromLTRB(12.0, 12.0, 12.0, 85.0), // Bottom padding for fixed image scroller
          itemCount: teamData.length,
          separatorBuilder: (_, __) => const Divider(height: 1),
          itemBuilder: (context, index) {
            String key = teamData.keys.elementAt(index);
            String value = teamData[key]!;
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 6.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 120,
                    child: Text(
                      key,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 13,
                      ),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      ": $value",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),

        // Fixed image scroller at bottom
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          child: Container(
            height: 75,
            padding: const EdgeInsets.fromLTRB(12.0, 4.0, 12.0, 8.0),
            color: Colors.white,
            child: _buildImageScroller(),
          ),
        ),
      ],
    );
  }

  Widget _buildElementDetailOverlay(ProjectDetailController controller) {
    final element = controller.selectedElement.value!;
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Column(
          children: [
            // Top section with image and close button
            Container(
              height: 400,
              child: Stack(
                children: [
                  // Main image
                  PageView.builder(
                    itemCount: element.images.length,
                    itemBuilder: (context, index) {
                      return Container(
                        width: double.infinity,
                        height: 400,
                        child: Image.network(
                          element.images[index],
                          fit: BoxFit.cover,
                        ),
                      );
                    },
                  ),

                  // Close button
                  Positioned(
                    top: 50,
                    right: 20,
                    child: GestureDetector(
                      onTap: () => controller.hideElements(),
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.6),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Bottom section with element details
            Expanded(
              child: Container(
                color: Colors.white,
                child: Column(
                  children: [
                    // Category tabs
                    Container(
                      color: ColorConstants.appColor,
                      padding: EdgeInsets.symmetric(vertical: 8),
                      child: SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          children: controller.elementCategories.map((category) => 
                            Container(
                              margin: EdgeInsets.only(right: 12),
                              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: element.category == category ? Colors.black : Colors.transparent,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.grey[300]!),
                              ),
                              child: Text(
                                category,
                                style: TextStyle(
                                  color: element.category == category ? Colors.white : Colors.black,
                                  fontSize: 14,
                                  fontWeight: element.category == category ? FontWeight.w600 : FontWeight.normal,
                                ),
                              ),
                            ),
                          ).toList(),
                        ),
                      ),
                    ),

                    // Element details
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Element name and favorite
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    element.name,
                                    style: TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                Icon(Icons.favorite_border, size: 24),
                                SizedBox(width: 16),
                                Icon(Icons.menu, size: 24),
                              ],
                            ),

                            SizedBox(height: 20),

                            // Element details
                            _buildElementDetailRow("Brand", element.brand),
                            _buildElementDetailRow("Collection", element.collection),
                            _buildElementDetailRow("Material", element.material),
                            _buildElementDetailRow("Price", element.price),

                            SizedBox(height: 20),

                            // Description
                            Text(
                              element.description,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                                height: 1.5,
                              ),
                            ),

                            SizedBox(height: 20),

                            // Similar images
                            Container(
                              height: 100,
                              child: ListView.builder(
                                scrollDirection: Axis.horizontal,
                                itemCount: element.images.length,
                                itemBuilder: (context, index) {
                                  return Container(
                                    width: 100,
                                    height: 100,
                                    margin: EdgeInsets.only(right: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.black, width: 1),
                                    ),
                                    child: ClipRRect(
                                      borderRadius: BorderRadius.circular(7),
                                      child: Image.network(
                                        element.images[index],
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }



  Widget _buildElementDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              ": $value",
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

