import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/project_sub_list_controller.dart';

class InstagramProfileScreen extends GetView<ProfileController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Safe<PERSON>rea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header section with avatar, stats, etc.
            //InstagramProfileHeader(),
            SizedBox(height: 10),
            // Highlights row
            //HighlightSection(),
            // Tab bar for Posts/Reels/Tagged
            TabBar(
              controller: controller.tabController,
              tabs: [
                Tab(icon: Icon(Icons.grid_on)),
                Tab(icon: Icon(Icons.ondemand_video)),
                Tab(icon: Icon(Icons.person_pin)),
              ],
              labelColor: Colors.white,
              unselectedLabelColor: Colors.grey,
              indicatorColor: Colors.white,
            ),
            // Tab views
            Expanded(
              child: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(
                controller: controller.tabController,
                children: [
                  // Posts grid
                  Padding(
                    padding: EdgeInsets.all(4),
                    child: GridView.builder(
                      itemCount: controller.postsImages.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          mainAxisSpacing: 4,
                          crossAxisSpacing: 4
                      ),
                      itemBuilder: (context, index) {
                        return Image.network(
                          controller.postsImages[index],
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                  // Reels grid (same layout)
                  Padding(
                    padding: EdgeInsets.all(4),
                    child: GridView.builder(
                      itemCount: controller.reelsImages.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          mainAxisSpacing: 4,
                          crossAxisSpacing: 4
                      ),
                      itemBuilder: (context, index) {
                        return Image.network(
                          controller.reelsImages[index],
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                  // Tagged grid (same layout)
                  Padding(
                    padding: EdgeInsets.all(4),
                    child: GridView.builder(
                      itemCount: controller.taggedImages.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          mainAxisSpacing: 4,
                          crossAxisSpacing: 4
                      ),
                      itemBuilder: (context, index) {
                        return Image.network(
                          controller.taggedImages[index],
                          fit: BoxFit.cover,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
