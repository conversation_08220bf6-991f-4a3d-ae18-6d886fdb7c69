import 'dart:math';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../common/mobilesize/sizeconfig.dart';
import '../../../common/widgets/cached_image_network_widget.dart';
import '../../../constants/assets.dart';
import '../../../utils/utils.dart';
import '../../bottom_navigation/controller/bottom_navigation_controller.dart';
import '../controller/projects_list_controller.dart';
import '../widgets/floating_tab_bar_toggle_widget.dart';

import '../widgets/projects_category_button_widget.dart';
import '../widgets/dynamic_project_grid.dart';

class ProjectListScreen extends StatefulWidget {
  @override
  State<ProjectListScreen> createState() => _ProjectListScreenState();
}

class _ProjectListScreenState extends State<ProjectListScreen> {
  final ProjectListController controller = Get.put(ProjectListController());
  final ScrollController _scrollController = ScrollController();
  final RxInt selectedIndex = 0.obs;
  final List<String> tabs = ['For You', 'Latest', 'Popular'];
  final ValueNotifier<bool> showStickyHeader = ValueNotifier(false);

  // Random positions for post sections (3-4 times throughout the list)
  List<int> adPositions = [];

  @override
  void initState() {
    super.initState();

    // Generate random positions for post sections
    adPositions = _generateRandomAdPositions();

    _scrollController.addListener(() {
      final offset = _scrollController.offset;
      final appBarHeight = 340; // Same as expandedHeight

      // Show sticky header only when app bar is collapsed
      showStickyHeader.value = offset > appBarHeight - kToolbarHeight;

      controller.updateStickyChipVisibility(offset);
    });
  }

  List<int> _generateRandomAdPositions() {
    final random = Random();
    final totalProjects = 10; // We have 10 projects now
    final numAds = 3 + random.nextInt(2); // 3-4 post sections
    final positions = <int>[];

    // Ensure post sections are spread throughout the list
    for (int i = 0; i < numAds; i++) {
      int position;
      do {
        position = 2 + random.nextInt(totalProjects + numAds - 2); // Start from position 2
      } while (positions.contains(position) || positions.any((p) => (p - position).abs() < 2));
      positions.add(position);
    }

    positions.sort();
    return positions;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    showStickyHeader.dispose();
    super.dispose();
  }

  Future<bool> _onWillPop() async {
    final navController = Get.find<BottomNavigationController>();
    final currentTabIndex = navController.selectedIndex.value;

    if (navController.navigatorKeys[currentTabIndex].currentState?.canPop() ?? false) {
      navController.navigatorKeys[currentTabIndex].currentState?.pop();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final categories = controller.categories;
    const itemsPerRow = 6;
    final rowCount = (categories.length / itemsPerRow).ceil();
    final currentTabIndex = Get.find<BottomNavigationController>().selectedIndex.value;
    final currentTabNavigatorId = currentTabIndex + 1;

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.blue.shade50,

        // Wrap your body in a Stack to overlay the top-right FAB
        body: Stack(
          children: [
            CustomScrollView(
              controller: _scrollController,
              slivers: [
                // ... your existing slivers unchanged
                SliverAppBar(
                  leading: IconButton(
                    onPressed: () {
                      Get.back(id: currentTabNavigatorId);
                    },
                    icon: const Icon(Icons.arrow_back_ios),
                  ),
                  title: ValueListenableBuilder<bool>(
                    valueListenable: showStickyHeader,
                    builder: (context, isCollapsed, child) {
                      return Align(
                        alignment: isCollapsed ? Alignment.centerLeft : Alignment.center,
                        child: Text(
                          "Projects",
                          textAlign: isCollapsed ? TextAlign.left : TextAlign.center,
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                  ),
                  actions: [
                    // Show icons in app bar only when collapsed
                    ValueListenableBuilder<bool>(
                      valueListenable: showStickyHeader,
                      builder: (context, isCollapsed, child) {
                        return isCollapsed
                            ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    margin: EdgeInsets.only(right: 8),
                                    child: IconButton(
                                      onPressed: () {
                                        print('Filter pressed');
                                      },
                                      icon: Icon(Icons.filter_list, color: Colors.white),
                                    ),
                                  ),
                                  Container(
                                    decoration: BoxDecoration(
                                      color: Colors.black.withValues(alpha: 0.3),
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    margin: EdgeInsets.only(right: 16),
                                    child: IconButton(
                                      onPressed: () {
                                        print('Search pressed');
                                      },
                                      icon: Icon(Icons.search, color: Colors.white),
                                    ),
                                  ),
                                ],
                              )
                            : SizedBox.shrink();
                      },
                    ),
                  ],
                  expandedHeight: 340,
                  pinned: true,
                  flexibleSpace: FlexibleSpaceBar(
                    collapseMode: CollapseMode.parallax,
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Positioned.fill(
                          child: Utils.pngImage(AssetStrings.projectsBgImage, width: 1.0 * width),
                        ),
                        Positioned.fill(
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                            child: Container(
                              color: Colors.black.withOpacity(0.0),
                            ),
                          ),
                        ),

                        // Filter and Search icons - show only when expanded
                        ValueListenableBuilder<bool>(
                          valueListenable: showStickyHeader,
                          builder: (context, isCollapsed, child) {
                            return !isCollapsed
                                ? Positioned(
                                    top: 120,
                                    right: 16,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black.withValues(alpha: 0.4),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: IconButton(
                                            onPressed: () {
                                              print('Filter pressed');
                                            },
                                            icon: Icon(Icons.filter_list, color: Colors.white),
                                          ),
                                        ),
                                        SizedBox(width: 8),
                                        Container(
                                          decoration: BoxDecoration(
                                            color: Colors.black.withValues(alpha: 0.4),
                                            borderRadius: BorderRadius.circular(20),
                                          ),
                                          child: IconButton(
                                            onPressed: () {
                                              print('Search pressed');
                                            },
                                            icon: Icon(Icons.search, color: Colors.white),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : SizedBox.shrink();
                          },
                        ),
                        Positioned(
                          bottom: -20,
                          left: 0,
                          right: 0,
                          child: SizedBox(
                            height: 150,
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: List.generate(rowCount, (rowIndex) {
                                  final startIndex = rowIndex * itemsPerRow;
                                  final endIndex = min(startIndex + itemsPerRow, categories.length);
                                  final rowItems = categories.sublist(startIndex, endIndex);

                                  return Padding(
                                    padding: const EdgeInsets.only(bottom: 10),
                                    child: Row(
                                      children: rowItems.map((category) {
                                        return ProjectCategoryButton(
                                          category: category,
                                          fontSize: 10,
                                          showColorBox: true,
                                          borderColor: Colors.black,
                                          color: Colors.black,
                                          onTap: () => Get.toNamed('/projectsSubListScreen', id: 2),
                                        );
                                      }).toList(),
                                    ),
                                  );
                                }),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                ValueListenableBuilder<bool>(
                  valueListenable: showStickyHeader,
                  builder: (context, shouldShow, child) {
                    return shouldShow
                        ? SliverPersistentHeader(
                      pinned: true,
                      delegate: _StickyHeaderDelegate(
                        child: SizedBox(
                          height: 50,
                          child: Container(
                            color: Colors.blue.shade50,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              child: Row(
                                children: controller.categories.map((category) {
                                  return ProjectCategoryButton(
                                    category: category,
                                    onTap: () => Get.toNamed('/projectsSubListScreen', id: 2),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                        : SliverToBoxAdapter(child: Container());
                  },
                ),
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                      if (adPositions.contains(index)) {
                        return _buildAdWidget();
                      }

                      final actualIndex = index - adPositions.where((pos) => pos < index).length;

                      return Obx(() {
                        if (actualIndex >= controller.projects.length) {
                          return Container();
                        }

                        return Column(
                          children: [
                            InkWell(
                              onTap: () {
                                controller.selectProject(controller.projects[actualIndex]);
                                Get.toNamed('/projectsDetailScreen', id: 2);
                              },
                              child: DynamicProjectGrid(
                                projects: [controller.projects[actualIndex]],
                                isProjectOfTheDay: actualIndex == 0,
                              ),
                            ),
                            // White separator container after each project
                            Container(
                              height: 12,
                              color: Colors.white,
                            ),
                          ],
                        );
                      });
                    },
                    childCount: controller.projects.length + adPositions.length,
                  ),
                ),
              ],
            ),

            // Top-right FloatingActionButton
            Positioned(
              top: 70,
              right: 16,
              child: GestureDetector(
                onTap: () {
                  print('Post a new project pressed');
                  // Your logic here
                },
                child: Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.yellow,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black26,
                        blurRadius: 4,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  alignment: Alignment.center,
                  padding: const EdgeInsets.all(8),
                  child: const Text(
                    'Post New Project!',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                      fontSize: 10,
                    ),
                  ),
                ),
              ),
            ),



          ],
        ),

        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        floatingActionButton: FloatingTabBarWithToggle(
          tabs: tabs,
          selectedIndex: selectedIndex,
          showToggle: false,

        ),
      ),
    );
  }


  Widget _buildAdWidget() {
    return Container(
      color: Colors.white, // White background to match reference
      height: 190,
      //padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "Posts : ",
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                TextSpan(
                  text: "Catchup on the updates",
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Container(
            color: Colors.black,
            child: SizedBox(
              height: 140,
              child: ListView.builder(
                itemCount: 4,
                scrollDirection: Axis.horizontal,
                //padding: const EdgeInsets.only(left: 12, right: 12), // Fixed padding for proper scroll
                itemBuilder: (context, adIndex) {
                  final studioNames = [
                    "Purple Studio",
                    "quirk studio",
                    "Ceric Studio",
                    "Muse Studio",
                    "Core Studio",
                  ];
                  final studioColors = [
                    Colors.deepOrange,
                    Colors.blue,
                    Colors.yellow,
                    Colors.pinkAccent,
                    Colors.green,
                  ];
            
                  return Padding(
                    padding: EdgeInsets.only(
                      right: adIndex == 3 ? 0 : 12.0, // No right padding for last item
                    ),
                    child: Container(
                      width: 140,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        //color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Center(
                        child: Container(
                          margin: const EdgeInsets.only(top: 10, bottom: 10), // Add margin for spacing
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.2),
                                blurRadius: 8,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          clipBehavior: Clip.antiAlias,
                          child: Stack(
                            children: [
                              Positioned.fill(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: CachedImageWidget(
                                    imageUrl: 'https://picsum.photos/id/10${adIndex + 1}/600/300',
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              // Light gradient for text readability
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  gradient: LinearGradient(
                                    colors: [Colors.black.withValues(alpha: 0.3), Colors.transparent],
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                    stops: const [0.0, 0.5],
                                  ),
                                ),
                              ),
                                Positioned(
                                  top: 8,
                                  left: 8,
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        radius: 8,
                                        backgroundColor: studioColors[adIndex % studioColors.length],
                                      ),
                                      const SizedBox(width: 6),
                                      Text(
                                        studioNames[adIndex],
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w500,
                                          fontSize: 13,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Place this helper widget somewhere in your file or as a separate widget
  Widget buildFacebookImageGrid(List<String> images) {
    int displayCount = images.length > 10 ? 10 : images.length;
    int extraCount = images.length - 10;

    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3, // 3 images per row, like Facebook
        mainAxisSpacing: 2,
        crossAxisSpacing: 2,
        childAspectRatio: 1,
      ),
      itemCount: displayCount,
      itemBuilder: (context, index) {
        // If this is the last visible image and there are extras, show overlay
        if (index == 9 && extraCount > 0) {
          return Stack(
            fit: StackFit.expand,
            children: [
              CachedImageWidget(
                imageUrl: images[index],
                fit: BoxFit.cover,
              ),
              Container(
                color: Colors.black.withOpacity(0.6),
                alignment: Alignment.center,
                child: Text(
                  '+$extraCount',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          );
        } else {
          return CachedImageWidget(
            imageUrl: images[index],
            fit: BoxFit.cover,
          );
        }
      },
    );
  }

}

class _StickyHeaderDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;

  _StickyHeaderDelegate({required this.child});

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => 50;

  @override
  double get minExtent => 50;

  @override
  bool shouldRebuild(_StickyHeaderDelegate oldDelegate) {
    return oldDelegate.child != child;
  }
}
