import 'package:flutter/material.dart';
import 'package:get/get.dart';

void main() {
  runApp(GetMaterialApp(home: ProjectsPage()));
}

class ProjectsController extends GetxController {
  var selectedTab = 0.obs;
  var categories = [
    "Residential",
    "Offices",
    "Studios",
    "Sports & Recreation",
    "Industrial Workspaces",
    "Retail",
    "Food & Beverage",
    "Hospitality",
    "Institutional",
    "Healthcare",
    "Civic & Government"
  ];
}

class ProjectsPage extends StatelessWidget {
  final ProjectsController controller = Get.put(ProjectsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xE6F2F5F8),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.brown[300]),
          onPressed: () {},
        ),
        title: Text(
          "PROJECTS",
          style: TextStyle(
            color: Colors.brown[300],
            fontWeight: FontWeight.w400,
            fontSize: 18,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.filter_list, color: Colors.brown[300]),
            onPressed: () {},
          ),
        ],
      ),
      body: Column(
        children: [
          // Categories
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                children: controller.categories.map((cat) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4.0),
                    child: Chip(
                      label: Text(cat),
                      backgroundColor: Color(0xFFD9E6EA),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
          SizedBox(height: 10),
          // Project of the Day
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0.0),
            child: Column(
              children: [
                Container(
                  width: double.infinity,
                  color: Colors.yellow,
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                  child: Row(
                    children: [
                      Text(
                        "PROJECT OF THE DAY",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.2,
                        ),
                      ),
                      Spacer(),
                      Text(
                        "Powered By",
                        style: TextStyle(fontSize: 12),
                      ),
                      SizedBox(width: 4),
                      Text(
                        "NITCO",
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Stack(
                  children: [
                    AspectRatio(
                      aspectRatio: 16 / 9,
                      child: Image.network(
                        'https://pplx-res.cloudinary.com/image/private/user_uploads/73076546/377a4e73-9830-419a-a176-4a3436adc1a0/IMG_0768.jpg',
                        fit: BoxFit.cover,
                      ),
                    ),
                    Positioned(
                      top: 10,
                      right: 10,
                      child: Icon(
                        Icons.star,
                        color: Colors.yellow,
                        size: 32,
                      ),
                    ),
                    Positioned(
                      bottom: 16,
                      left: 16,
                      child: Row(
                        children: [
                          CircleAvatar(
                            radius: 16,
                            backgroundColor: Colors.white70,
                            child: Text(
                              "MuseLAB",
                              style: TextStyle(fontSize: 10, color: Colors.black),
                            ),
                          ),
                          SizedBox(width: 8),
                          Text(
                            "Open House",
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              backgroundColor: Colors.black45,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Spacer(),
          // Bottom Tabs
          Container(
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [BoxShadow(color: Colors.black12, blurRadius: 4)],
            ),
            child: Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildTab("For You", 0, controller),
                _buildTab("Latest", 1, controller),
                _buildTab("Popular", 2, controller),
              ],
            )),
          ),
          SizedBox(height: 10),
        ],
      ),
      bottomNavigationBar: BottomAppBar(
        color: Colors.white,
        elevation: 8,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            IconButton(icon: Icon(Icons.home_outlined), onPressed: () {}),
            IconButton(icon: Icon(Icons.star_border), onPressed: () {}),
            IconButton(icon: Icon(Icons.search), onPressed: () {}),
            IconButton(icon: Icon(Icons.add_circle_outline), onPressed: () {}),
          ],
        ),
      ),
    );
  }

  Widget _buildTab(String label, int index, ProjectsController controller) {
    return GestureDetector(
      onTap: () => controller.selectedTab.value = index,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
        decoration: BoxDecoration(
          color: controller.selectedTab.value == index
              ? Colors.white
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: controller.selectedTab.value == index
                ? Colors.black
                : Colors.grey,
            fontWeight: controller.selectedTab.value == index
                ? FontWeight.bold
                : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
