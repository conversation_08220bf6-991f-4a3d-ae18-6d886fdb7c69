import 'package:flutter/material.dart';

import '../../../constants/assets.dart';
import '../../../utils/utils.dart';

class ProjectCategoryButton extends StatelessWidget {
  final String category;
  final Color color;
  final Color borderColor;
  final double fontSize;
  final bool showColorBox; // NEW FLAG
  final VoidCallback? onTap;

  const ProjectCategoryButton({
    Key? key,
    required this.category,
    this.color = Colors.black,
    this.borderColor = const Color(0xFFBDBDBD),
    this.fontSize = 14,
    this.showColorBox = true, // Default: show it
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(20),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 1),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(5),
            border: Border.all(color: borderColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showColorBox)
                Container(
                  width: 10,
                  height: 10,
                  margin: const EdgeInsets.only(right: 6),
                  child: Utils.pngImage(AssetStrings.projectsBgImage),
                ),
              Text(
                category,
                style: TextStyle(fontSize: fontSize),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

