import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FloatingTabBarWithToggle extends StatelessWidget {
  final List<String> tabs;
  final RxInt selectedIndex;
  final RxInt? crossAxisCount; // <-- made nullable
  final IconData Function(int)? getGridIcon; // <-- made nullable
  final bool showToggle;

  const FloatingTabBarWithToggle({
    Key? key,
    required this.tabs,
    required this.selectedIndex,
    this.crossAxisCount,
    this.getGridIcon,
    this.showToggle = true,
  }) : assert(!showToggle || (crossAxisCount != null && getGridIcon != null),
  'crossAxisCount and getGridIcon must be provided if showToggle is true.'),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              borderRadius: BorderRadius.circular(30),
            ),
            padding: const EdgeInsets.all(4),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: List.generate(tabs.length, (index) {
                final isSelected = selectedIndex.value == index;
                return GestureDetector(
                  onTap: () => selectedIndex.value = index,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.white : Colors.transparent,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: Text(
                      tabs[index],
                      style: TextStyle(
                        color: isSelected ? Colors.black : Colors.white,
                        fontWeight: FontWeight.w500,
                        fontSize: 12
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),

          /// Toggle Icon
          if (showToggle && crossAxisCount != null && getGridIcon != null)
            GestureDetector(
              onTap: () {
                crossAxisCount!.value = (crossAxisCount!.value % 3) + 1;
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                child: Obx(() {
                  return Icon(
                    getGridIcon!(crossAxisCount!.value),
                    color: Colors.white,
                  );
                }),
              ),
            ),
        ],
      );
    });
  }
}
