import 'dart:math';
import 'package:flutter/material.dart';
import '../../../common/widgets/cached_image_network_widget.dart';

class FacebookStyleImageGrid extends StatelessWidget {
  final List<String> images;
  final double height;
  final double borderRadius;

  const FacebookStyleImageGrid({
    Key? key,
    required this.images,
    this.height = 220,
    this.borderRadius = 12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    int count = images.length;
    if (count == 0) return SizedBox.shrink();

    int displayCount = count > 4 ? 4 : count;
    int extra = count - 4;

    Widget buildImage(String url, {BorderRadius? br, Widget? overlay}) {
      return ClipRRect(
        borderRadius: br ?? BorderRadius.circular(borderRadius),
        child: Stack(
          fit: StackFit.expand,
          children: [
            CachedImageWidget(
              imageUrl: url,
              fit: BoxFit.cover,
            ),
            if (overlay != null) overlay,
          ],
        ),
      );
    }

    if (count == 1) {
      return SizedBox(
        height: height,
        child: buildImage(images[0]),
      );
    } else if (count == 2) {
      return SizedBox(
        height: height,
        child: Row(
          children: [
            Expanded(child: buildImage(images[0], br: BorderRadius.only(
              topLeft: Radius.circular(borderRadius),
              bottomLeft: Radius.circular(borderRadius),
            ))),
            SizedBox(width: 2),
            Expanded(child: buildImage(images[1], br: BorderRadius.only(
              topRight: Radius.circular(borderRadius),
              bottomRight: Radius.circular(borderRadius),
            ))),
          ],
        ),
      );
    } else if (count == 3) {
      final isPatternA = Random().nextBool(); // true = large left, false = large right

      return SizedBox(
        height: height,
        child: Row(
          children: isPatternA
              ? [
            /// Pattern A: Large on left, two stacked on right
            Expanded(
              flex: 2,
              child: buildImage(images[0], br: BorderRadius.only(
                topLeft: Radius.circular(borderRadius),
                bottomLeft: Radius.circular(borderRadius),
              )),
            ),
            SizedBox(width: 2),
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Expanded(
                    child: buildImage(images[1], br: BorderRadius.only(
                      topRight: Radius.circular(borderRadius),
                    )),
                  ),
                  SizedBox(height: 2),
                  Expanded(
                    child: buildImage(images[2], br: BorderRadius.only(
                      bottomRight: Radius.circular(borderRadius),
                    )),
                  ),
                ],
              ),
            ),
          ]
              : [
            /// Pattern B: Two stacked on left, large on right
            Expanded(
              flex: 1,
              child: Column(
                children: [
                  Expanded(
                    child: buildImage(images[0], br: BorderRadius.only(
                      topLeft: Radius.circular(borderRadius),
                    )),
                  ),
                  SizedBox(height: 2),
                  Expanded(
                    child: buildImage(images[1], br: BorderRadius.only(
                      bottomLeft: Radius.circular(borderRadius),
                    )),
                  ),
                ],
              ),
            ),
            SizedBox(width: 2),
            Expanded(
              flex: 2,
              child: buildImage(images[2], br: BorderRadius.only(
                topRight: Radius.circular(borderRadius),
                bottomRight: Radius.circular(borderRadius),
              )),
            ),
          ],
        ),
      );
    } else {
      return SizedBox(
        height: height,
        child: Row(
          children: [
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: buildImage(images[0], br: BorderRadius.only(
                      topLeft: Radius.circular(borderRadius),
                    )),
                  ),
                  SizedBox(height: 2),
                  Expanded(
                    child: buildImage(images[2], br: BorderRadius.only(
                      bottomLeft: Radius.circular(borderRadius),
                    )),
                  ),
                ],
              ),
            ),
            SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: buildImage(images[1], br: BorderRadius.only(
                      topRight: Radius.circular(borderRadius),
                    )),
                  ),
                  SizedBox(height: 2),
                  Expanded(
                    child: buildImage(
                      images[3],
                      br: BorderRadius.only(
                        bottomRight: Radius.circular(borderRadius),
                      ),
                      overlay: extra > 0
                          ? Container(
                        color: Colors.black.withOpacity(0.5),
                        alignment: Alignment.center,
                        child: Text(
                          '+$extra',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      )
                          : null,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }
}
