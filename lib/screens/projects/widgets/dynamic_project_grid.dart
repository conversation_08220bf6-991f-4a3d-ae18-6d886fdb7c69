import 'dart:math';
import 'package:flutter/material.dart';
import '../../../common/widgets/cached_image_network_widget.dart';
import '../../../models/project_model.dart';

class DynamicProjectGrid extends StatelessWidget {
  final List<ProjectModel> projects;
  final bool isProjectOfTheDay;

  const DynamicProjectGrid({
    super.key,
    required this.projects,
    this.isProjectOfTheDay = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      //margin: const EdgeInsets.symmetric(vertical: 3, horizontal: 8),
      decoration: BoxDecoration(
        border: isProjectOfTheDay
            ? Border.all(color: Colors.yellow, width: 4)
            : null,
      ),
      child: Column(
        children: [
          // Project of the Day header
          if (isProjectOfTheDay)
            Container(
              color: Colors.yellow,
              padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 1),
              child: Row(
                children: [
                  const Text(
                    'PROJECT OF THE DAY',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                      letterSpacing: 1.2,
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: const [
                      Text('Powered By', style: TextStyle(fontSize: 12, color: Colors.black)),
                      Text('NITCO', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.black)),
                    ],
                  ),
                ],
              ),
            ),

          // Black section at top with studio info - no overlap
          Container(
            color: Colors.black,
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: 10,
                  child: Text(
                    projects.first.studioInitial,
                    style: const TextStyle(
                      color: Colors.redAccent,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      projects.first.projectName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                    Text(
                      projects.first.studioName,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                const Icon(
                  Icons.star,
                  color: Colors.yellow,
                  size: 20,
                ),
                const SizedBox(width: 8),
                PopupMenuButton<int>(
                  offset: const Offset(0, 40),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 1,
                      child: Text('Add to bookmark'),
                    ),
                    const PopupMenuItem(
                      value: 2,
                      child: Text('Follow'),
                    ),
                    const PopupMenuItem(
                      value: 3,
                      child: Text('Share'),
                    ),
                  ],
                  child: const Icon(
                    Icons.more_vert,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          // Dynamic grid layout - separate from black section
          _buildDynamicGrid(),
        ],
      ),
    );
  }

  Widget _buildDynamicGrid() {
    final random = Random();
    final gridType = random.nextInt(4); // 4 different grid patterns
    
    switch (gridType) {
      case 0:
        return _buildPattern1(); // Large left, small grid right
      case 1:
        return _buildPattern2(); // Large right, small grid left
      case 2:
        return _buildPattern3(); // Two large horizontal
      case 3:
        return _buildPattern4(); // Grid of 4
      default:
        return _buildPattern1();
    }
  }

  // Pattern 1: Large image on left, 2x2 grid on right
  Widget _buildPattern1() {
    return Container(
      height: 450,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              decoration: const BoxDecoration(
                border: Border(right: BorderSide(color: Colors.black, width: 2)),
              ),
              child: _buildImageContainer(projects[0].images[0]),
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              children: [
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(bottom: BorderSide(color: Colors.black, width: 2)),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              border: Border(right: BorderSide(color: Colors.black, width: 2)),
                            ),
                            child: _buildImageContainer(projects[0].images[1]),
                          ),
                        ),
                        Expanded(child: _buildImageContainer(projects[0].images[2])),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          decoration: const BoxDecoration(
                            border: Border(right: BorderSide(color: Colors.black, width: 2)),
                          ),
                          child: _buildImageContainer(projects[0].images[3]),
                        ),
                      ),
                      Expanded(
                        child: _buildImageContainer(
                          projects[0].images[4],
                          extraCount: projects[0].images.length > 5 ? projects[0].images.length - 5 : 0,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Pattern 2: 2x2 grid on left, large image on right
  Widget _buildPattern2() {
    return Container(
      height: 450,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Container(
              decoration: const BoxDecoration(
                border: Border(right: BorderSide(color: Colors.black, width: 2)),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(bottom: BorderSide(color: Colors.black, width: 2)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: const BoxDecoration(
                                border: Border(right: BorderSide(color: Colors.black, width: 2)),
                              ),
                              child: _buildImageContainer(projects[0].images[0]),
                            ),
                          ),
                          Expanded(child: _buildImageContainer(projects[0].images[1])),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              border: Border(right: BorderSide(color: Colors.black, width: 2)),
                            ),
                            child: _buildImageContainer(projects[0].images[2]),
                          ),
                        ),
                        Expanded(
                          child: _buildImageContainer(
                            projects[0].images[3],
                            extraCount: projects[0].images.length > 4 ? projects[0].images.length - 4 : 0,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: _buildImageContainer(projects[0].images[4]),
          ),
        ],
      ),
    );
  }

  // Pattern 3: Two large horizontal images
  Widget _buildPattern3() {
    return Container(
      height: 450,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.black, width: 2)),
              ),
              child: _buildImageContainer(projects[0].images[0]),
            ),
          ),
          Expanded(
            child: _buildImageContainer(
              projects[0].images[1],
              extraCount: projects[0].images.length > 2 ? projects[0].images.length - 2 : 0,
            ),
          ),
        ],
      ),
    );
  }

  // Pattern 4: 2x2 grid
  Widget _buildPattern4() {
    return Container(
      height: 450,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: Column(
        children: [
          Expanded(
            child: Container(
              decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.black, width: 2)),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      decoration: const BoxDecoration(
                        border: Border(right: BorderSide(color: Colors.black, width: 2)),
                      ),
                      child: _buildImageContainer(projects[0].images[0]),
                    ),
                  ),
                  Expanded(child: _buildImageContainer(projects[0].images[1])),
                ],
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: const BoxDecoration(
                      border: Border(right: BorderSide(color: Colors.black, width: 2)),
                    ),
                    child: _buildImageContainer(projects[0].images[2]),
                  ),
                ),
                Expanded(
                  child: _buildImageContainer(
                    projects[0].images[3],
                    extraCount: projects[0].images.length > 4 ? projects[0].images.length - 4 : 0,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageContainer(String imageUrl, {int extraCount = 0}) {
    return Stack(
      fit: StackFit.expand,
      children: [
        CachedImageWidget(
          imageUrl: imageUrl,
          fit: BoxFit.cover,
        ),
        if (extraCount > 0)
          Container(
            color: Colors.black.withValues(alpha: 0.4),
            alignment: Alignment.center,
            child: Text(
              '+$extraCount',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                //fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }
}


