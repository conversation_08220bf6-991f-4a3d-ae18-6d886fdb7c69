import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'data.dart';

class ProjectsSubListController extends GetxController {
  RxList<String> categories = [
    'Residential', 'Offices', 'Studios', 'Sports & Recreation',
    'Industrial', 'Retail', 'Food & Beverage', 'Hospitality',
    'Institutional', 'Healthcare', 'Civic & Govt',
    'Institutional', 'Healthcare', 'Civic & Govt',
  ].obs;

  final RxInt crossAxisCount = 3.obs; // Default is 3 columns

  final List<String> postsImages = [];
  var imageList = <String>[].obs;
  var imageAssetsList = <String>[].obs;
  var selectedCategories = <String>[].obs;
  RxBool showStickyHorizontalChips = false.obs;

  /// Assume 15 projects for now (replace with real project list later)
  final int totalProjects = 15;
  RxList<int> currentCarouselIndices = <int>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchImages(); // Simulate API call
    fetchAssetImages(); // Load local images

    // Initialize carousel indices for each project
    currentCarouselIndices.value = List<int>.filled(totalProjects, 0);
  }

  void toggleCategory(String category) {
    if (selectedCategories.contains(category)) {
      selectedCategories.remove(category);
    } else {
      selectedCategories.add(category);
    }
  }

  void fetchImages() {
    imageList.value = [
      'https://picsum.photos/id/1018/600/300',
      'https://picsum.photos/id/1015/600/300',
      'https://picsum.photos/id/1016/600/300',
    ];
    postsImages.addAll(Data.postsImages);
  }

  void fetchAssetImages() {
    imageAssetsList.value = [
      'assets/images/projects/user-card1.png',
      'assets/images/projects/user-card2.png',
      'assets/images/projects/user-card3.png',
    ];
  }

  void updateStickyChipVisibility(double offset) {
    if (offset > 100 && !showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = true;
    } else if (offset <= 100 && showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = false;
    }
  }

  /// 👇 Add this method
  IconData getGridIcon(int count) {
    switch (count) {
      case 1:
        return Icons.view_agenda_rounded;
      case 2:
        return Icons.view_module_rounded;
      case 3:
      default:
        return Icons.grid_view_rounded;
    }
  }
}
