import 'package:get/get.dart';
import '../../../models/project_model.dart';
import '../widgets/dynamic_project_grid.dart';

class ProjectListController extends GetxController {
  RxList<String> categories = [
    'Residential', 'Offices', 'Studios', 'Sports & Recreation',
    'Industrial', 'Retail', 'Food & Beverage', 'Hospitality',
    'Institutional', 'Healthcare', 'Civic & Govt',
    'Institutional', 'Healthcare', 'Civic & Govt',
  ].obs;

  var imageList = <String>[].obs;
  var imageAssetsList = <String>[].obs;
  var selectedCategories = <String>[].obs;
  RxBool showStickyHorizontalChips = false.obs;
  RxInt selectedItem = RxInt(-1);

  /// Assume 15 projects for now (replace with real project list later)
  final int totalProjects = 15;
  RxList<int> currentCarouselIndices = <int>[].obs;

  // Project data for dynamic grid
  RxList<ProjectModel> projects = <ProjectModel>[].obs;

  // Selected project for detail view
  Rx<ProjectModel?> selectedProject = Rx<ProjectModel?>(null);

  @override
  void onInit() {
    super.onInit();
    fetchImages(); // Simulate API call
    fetchProjects(); // Fetch project data

    // Initialize carousel indices for each project
    currentCarouselIndices.value = List<int>.filled(totalProjects, 0);
  }

  void toggleCategory(String category) {
    if (selectedCategories.contains(category)) {
      selectedCategories.remove(category);
    } else {
      selectedCategories.add(category);
    }
  }

  void fetchImages() {
    imageList.value = [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80', // Modern dining room with table, chairs, and kitchen
      'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80', // Cozy living room with natural lighting
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80', // Modern interior
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80', // Wood Interior
      'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80', // Wood Interior
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80', // Wood Interior
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80', // Wood Interior
    ];
  }

  void fetchProjects() {
    projects.value = [
      ProjectModel.sample(id: "1", projectName: "Open House", studioName: "MuseLab", studioInitial: "M", isProjectOfTheDay: true),
      ProjectModel.sample(id: "2", projectName: "Pastel den", studioName: "Quirk Studio", studioInitial: "Q"),
      ProjectModel.sample(id: "3", projectName: "Zen living", studioName: "Sumesh Menon Designs", studioInitial: "S"),
      ProjectModel.sample(id: "4", projectName: "Modern Loft", studioName: "Urban Designs", studioInitial: "U"),
      ProjectModel.sample(id: "5", projectName: "Cozy Cabin", studioName: "Nature Studio", studioInitial: "N"),
      ProjectModel.sample(id: "6", projectName: "Luxury Villa", studioName: "Elite Interiors", studioInitial: "E"),
      ProjectModel.sample(id: "7", projectName: "Minimalist Space", studioName: "Clean Design Co", studioInitial: "C"),
      ProjectModel.sample(id: "8", projectName: "Industrial Chic", studioName: "Raw Studio", studioInitial: "R"),
      ProjectModel.sample(id: "9", projectName: "Scandinavian Home", studioName: "Nordic Designs", studioInitial: "N"),
      ProjectModel.sample(id: "10", projectName: "Art Deco Revival", studioName: "Vintage Studio", studioInitial: "V"),
    ];
  }

  void selectProject(ProjectModel project) {
    selectedProject.value = project;
  }


  void updateStickyChipVisibility(double offset) {
    if (offset > 200 && !showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = true;
    } else if (offset <= 200 && showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = false;
    }
  }
}
