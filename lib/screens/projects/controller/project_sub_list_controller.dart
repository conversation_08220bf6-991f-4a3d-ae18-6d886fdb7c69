import 'package:get/get.dart';
import 'package:flutter/material.dart';

import 'data.dart';


class ProfileController extends GetxController with GetSingleTickerProviderStateMixin {
  // Observable state variables
  RxInt postsCount = 0.obs;
  RxInt followersCount = 0.obs;
  RxInt followingCount = 0.obs;
  RxString username = ''.obs;
  RxString category = ''.obs;
  RxString bio = ''.obs;

  late TabController tabController;

  //final List<HighlightStory> highlightStories = [];
  final List<String> postsImages = [];
  final List<String> reelsImages = [];
  final List<String> taggedImages = [];

  @override
  void onInit() {
    super.onInit();
    // Load data into observables
    username.value = Data.username;
    category.value = Data.category;
    bio.value = Data.bio;
    postsCount.value = Data.posts;
    followersCount.value = Data.followers;
    followingCount.value = Data.following;
    //highlightStories.addAll(Data.highlightStories);
    postsImages.addAll(Data.postsImages);
    reelsImages.addAll(Data.reelsImages);
    taggedImages.addAll(Data.taggedImages);

    // Initialize TabController (for 3 tabs)
    tabController = TabController(length: 3, vsync: this);
    tabController.addListener(() {
      // You could react to tab changes here if needed
    });
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
