import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:openslate/utils/utils.dart';
import '../../../constants/assets.dart';
import '../../../constants/colors.dart';
import '../../../services/responsive_service.dart';
import '../../../utils/routes.dart';
import '../../onBoarding/pages/welcome_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {

  @override
  void initState() {
    super.initState();

    // Timer to simulate the splash screen delay
    Timer(const Duration(seconds: 3), () {
      Get.to(() => const WelcomeScreen());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.appColor,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
            statusBarColor: ColorConstants.appColor,
            statusBarIconBrightness: Brightness.dark,
            statusBarBrightness: Brightness.light // For iOS (dark icons)
        ),
        backgroundColor: ColorConstants.appColor,
        //automaticallyImplyLeading: false,
        toolbarHeight: 0,
        elevation: 0,
      ),
      body: Center(
        child: Utils.pngImage(AssetStrings.splashImage),
      ),
    );
  }
}