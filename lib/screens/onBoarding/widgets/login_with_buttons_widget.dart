import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/routes.dart';

void showAuthBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    backgroundColor: Colors.white,
    builder: (context) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            _authButton(
              icon: Icons.mail_outline,
              label: "Continue with Mail",
              onTap: () => Get.toNamed(Routes.signInScreen),
            ),
            const SizedBox(height: 12),
            _authButton(
              icon: Icons.apple,
              label: "Continue with Apple",
              onTap: () {}, // TODO
            ),
            const SizedBox(height: 12),
            _authButton(
              icon: Icons.g_mobiledata,
              label: "Continue with Google",
              onTap: () {}, // TODO
            ),
            const SizedBox(height: 12),
            _authButton(
              icon: Icons.person_outline,
              label: "Continue as Guest",
              onTap: () => Get.toNamed(Routes.bottomNavigationScreen),
            ),
            const SizedBox(height: 16),
            const Text(
              "By joining, you agree with our\nTerms of Services and Privacy Policy.",
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 16),
          ],
        ),
      );
    },
  );
}

Widget _authButton({
  IconData? icon,
  String? iconAsset,
  required String label,
  required VoidCallback onTap,
}) {
  return InkWell(
    onTap: onTap,
    borderRadius: BorderRadius.circular(12),
    child: Container(
      height: 48,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (iconAsset != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Image.asset(iconAsset, height: 20),
            )
          else if (icon != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: Icon(icon, size: 20),
            ),
          Text(label, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    ),
  );
}
