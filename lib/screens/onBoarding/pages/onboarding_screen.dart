import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../common/widgets/onboardind_screen_buttons.dart';
import '../../../constants/assets.dart';
import '../../../utils/routes.dart';
import '../../../utils/utils.dart';
import '../controller/onboarding_controller.dart';
import '../widgets/login_with_buttons_widget.dart';

class OnBoardingScreen extends StatefulWidget {
  const OnBoardingScreen({super.key});

  @override
  State<OnBoardingScreen> createState() => _OnBoardingScreenState();
}

class _OnBoardingScreenState extends State<OnBoardingScreen> {
  final controller = Get.put(OnBoardingController());
  final pageController = PageController();

  final List<String> titles = [
    "Get inspired",
    "Get creative",
    "Get started"
  ];

  final List<String> descriptions = [
    "Lorem ipsum dolor sit amet consectetur. Porttitor viverra consequat a donec etiam sem quam...",
    "Discover and explore unique interior inspirations for your perfect space.",
    "You're all set! Begin your design journey now with our curated tools."
  ];
  final List<String> images = [
    AssetStrings.CardImageIcon,     // Image for page 0
    AssetStrings.CardImageIcon,  // Image for page 1
    AssetStrings.CardImageIcon,   // Image for page 2
  ];

  String imageForPage(int index) {
    if (index < images.length) {
      return images[index];
    }
    return AssetStrings.CardImageIcon; // default fallback
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xffF4F4F4),
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Utils.pngImage(AssetStrings.LogoImageIcon),
                  TextButton(
                    onPressed: () {
                      showAuthBottomSheet(context);

                    },
                    child: const Text(
                      "sign in",
                      style: TextStyle(color: Colors.black),
                    ),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6), // Less space inside
                      minimumSize: Size.zero, // Removes default button size constraints
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap, // Avoid extra padding
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16), // smaller radius
                        side: BorderSide(color: Colors.black12),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: PageView.builder(
                controller: pageController,
                itemCount: titles.length,
                onPageChanged: (index) => controller.setPage(index),
                itemBuilder: (context, index) {
                  return Column(
                    children: [
                      Utils.pngImage(imageForPage(index)),
                      Text(titles[index], style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                        child: Text(
                          descriptions[index],
                          textAlign: TextAlign.center,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      Obx(() => Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(titles.length, (i) {
                          final isActive = controller.currentPage.value == i;
                          return AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            width: isActive ? 20 : 6, // Expanded line vs dot
                            height: 6,
                            decoration: BoxDecoration(
                              color: isActive ? Colors.black : Colors.grey.shade400,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          );
                        }),
                      ))
                    ],
                  );
                },
              ),
            ),
            Obx(() {
              final page = controller.currentPage.value;
              return Padding(
                padding: const EdgeInsets.all(16.0),
                child: page == 0 ? _bottomLayoutOne() : _bottomLayoutTwo(),
              );
            })
          ],
        ),
      ),
    );
  }


  Widget _bottomLayoutOne() {
    return Row(
      children: [
        // Get Started Button
        Expanded(
          child: CommonButtons.getStarted(
            onPressed: () {
              Get.toNamed(Routes.signUpScreen);
            },
          ),
        ),
        const SizedBox(width: 12),

        // Explore Button
        Expanded(
          child: CommonButtons.explore(
            onPressed: () {
              pageController.animateToPage(
                1,
                duration: const Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
              controller.setPage(1); // update the page indicator
            },
          ),
        ),
      ],
    );
  }

  Widget _bottomLayoutTwo() {
    final currentPage = controller.currentPage.value;
    final isLastPage = currentPage == titles.length - 1;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.black12),
            borderRadius: BorderRadius.circular(8),
          ),
          child: IconButton(
            icon: const Icon(Icons.chevron_left),
            onPressed: () => controller.prevPage(),
          ),
        ),

        const SizedBox(width: 12),

        Expanded(
          child: CommonButtons.getStarted(
            onPressed: () {
              if (isLastPage) {
                Get.toNamed(Routes.signUpScreen);
              } else {
                controller.nextPage(titles.length);
              }
            },
          ),
        ),

        const SizedBox(width: 12),

        if (!isLastPage)
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.black12),
              borderRadius: BorderRadius.circular(8),
            ),
            child: IconButton(
              icon: const Icon(Icons.chevron_right),
              onPressed: () => controller.nextPage(titles.length),
            ),
          ),
      ],
    );
  }
}
