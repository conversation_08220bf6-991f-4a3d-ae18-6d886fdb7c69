import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../common/widgets/responsive_wrapper.dart';
import '../../../constants/colors.dart';
import '../../../services/connectivity_service.dart';
import '../../../services/responsive_service.dart';
import '../../../utils/routes.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final responsiveService = ResponsiveService.to;

    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: ResponsiveWrapper(
        child: <PERSON><PERSON><PERSON>(
          child: Column(
            children: [
              // Header with logo
              Padding(
                padding: EdgeInsets.symmetric(
                  vertical: responsiveService.responsiveValue(
                    mobile: 20.0,
                    tablet: 30.0,
                    desktop: 40.0,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ResponsiveText(
                      'OpenSlate',
                      baseFontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.deepBlue,
                    ),
                  ],
                ),
              ),

              // Main content
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Welcome illustration placeholder
                    ResponsiveContainer(
                      mobileWidth: responsiveService.widthPercent(70),
                      tabletWidth: responsiveService.widthPercent(50),
                      desktopWidth: responsiveService.widthPercent(30),
                      mobileHeight: responsiveService.heightPercent(30),
                      tabletHeight: responsiveService.heightPercent(35),
                      desktopHeight: responsiveService.heightPercent(40),
                      decoration: BoxDecoration(
                        color: ColorConstants.lightGrey,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.home_work_outlined,
                        size: responsiveService.responsiveValue(
                          mobile: 80.0,
                          tablet: 100.0,
                          desktop: 120.0,
                        ),
                        color: ColorConstants.deepBlue,
                      ),
                    ),

                    SizedBox(height: responsiveService.responsiveValue(
                      mobile: 40.0,
                      tablet: 50.0,
                      desktop: 60.0,
                    )),

                    // Welcome title
                    ResponsiveText(
                      'Welcome to OpenSlate',
                      baseFontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: ColorConstants.darkBlack,
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: responsiveService.responsiveValue(
                      mobile: 16.0,
                      tablet: 20.0,
                      desktop: 24.0,
                    )),

                    // Welcome description
                    Padding(
                      padding: EdgeInsets.symmetric(
                        horizontal: responsiveService.responsiveValue(
                          mobile: 32.0,
                          tablet: 60.0,
                          desktop: 100.0,
                        ),
                      ),
                      child: ResponsiveText(
                        'Discover amazing interior designs, connect with professionals, and bring your dream space to life.',
                        baseFontSize: 16,
                        color: ColorConstants.gunSmoke,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ),

              // Bottom buttons
              Padding(
                padding: EdgeInsets.all(
                  responsiveService.responsiveValue(
                    mobile: 24.0,
                    tablet: 32.0,
                    desktop: 40.0,
                  ),
                ),
                child: Column(
                  children: [
                    // Get Started button
                    SizedBox(
                      width: double.infinity,
                      height: responsiveService.responsiveValue(
                        mobile: 50.0,
                        tablet: 55.0,
                        desktop: 60.0,
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          Get.toNamed(Routes.bottomNavigationScreen, arguments: {'index': 1});
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorConstants.deepBlue,
                          foregroundColor: ColorConstants.white,
                          elevation: 2,
                          shadowColor: ColorConstants.deepBlue.withValues(alpha: 0.3),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: ResponsiveText(
                          'Get Started',
                          baseFontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: ColorConstants.white,
                        ),
                      ),
                    ),

                    SizedBox(height: responsiveService.responsiveValue(
                      mobile: 16.0,
                      tablet: 20.0,
                      desktop: 24.0,
                    )),

                    // Sign In button
                    SizedBox(
                      width: double.infinity,
                      height: responsiveService.responsiveValue(
                        mobile: 50.0,
                        tablet: 55.0,
                        desktop: 60.0,
                      ),
                      child: OutlinedButton(
                        onPressed: () {
                          Get.toNamed(Routes.signInScreen);
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: ColorConstants.deepBlue,
                          side: const BorderSide(color: ColorConstants.deepBlue, width: 1.5),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: ResponsiveText(
                          'Sign In',
                          baseFontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: ColorConstants.deepBlue,
                        ),
                      ),
                    ),

                    SizedBox(height: responsiveService.responsiveValue(
                      mobile: 16.0,
                      tablet: 20.0,
                      desktop: 24.0,
                    )),

                    // Sign Up link
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ResponsiveText(
                          "Don't have an account? ",
                          baseFontSize: 14,
                          color: ColorConstants.gunSmoke,
                        ),
                        GestureDetector(
                          onTap: () {
                            Get.toNamed(Routes.signUpScreen);
                          },
                          child: ResponsiveText(
                            'Sign Up',
                            baseFontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: ColorConstants.deepBlue,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: responsiveService.responsiveValue(
                      mobile: 16.0,
                      tablet: 20.0,
                      desktop: 24.0,
                    )),

                    // Test No Internet Button (for testing purposes)
                    TextButton(
                      onPressed: () {
                        Get.toNamed(Routes.noInternetScreen);
                      },
                      child: ResponsiveText(
                        'Test No Internet Screen',
                        baseFontSize: 12,
                        color: ColorConstants.gunSmoke,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
