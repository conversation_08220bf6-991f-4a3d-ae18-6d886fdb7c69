import 'package:get/get.dart';

class WishlistController extends GetxController {
  var wishlist = <Map<String, dynamic>>[].obs;
  var isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadDummyWishlist();
  }

  void loadDummyWishlist() {
    isLoading(true);
    try {
      wishlist.value = [
        {
          "category": "Furnitures",
          "items": [
            {"image": "assets/images/wishlist/Frame 18384.png", "isVideo": false},
            {"image": "assets/images/wishlist/Frame 18386.png","isVideo": false},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
          ],
        },
        {
          "category": "Sculptures",
          "items": [
            {"image": "assets/images/wishlist/Frame 18384.png", "isVideo": false},
            {"image": "assets/images/wishlist/Frame 18386.png","isVideo": false},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
          ],
        },
        {
          "category": "Wall Arts",
          "items": [
            {"image": "assets/images/wishlist/Frame 18384.png", "isVideo": false},
            {"image": "assets/images/wishlist/Frame 18386.png","isVideo": false},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18389.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18390.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (1).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (2).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (3).png", "isVideo": true},
          ],
        },
        {
          "category": "Abstract",
          "items": [
            {"image": "assets/images/wishlist/Frame 18384.png", "isVideo": false},
            {"image": "assets/images/wishlist/Frame 18386.png","isVideo": false},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18389.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18390.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (1).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (2).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (3).png", "isVideo": true},
          ],
        },
        {
          "category": "Sculptures",
          "items": [
            {"image": "assets/images/wishlist/Frame 18384.png", "isVideo": false},
            {"image": "assets/images/wishlist/Frame 18386.png","isVideo": false},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18389.png", "isVideo": true},
            {"image": "assets/images/wishlist/Frame 18390.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic.png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (1).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (2).png", "isVideo": true},
            {"image": "assets/images/wishlist/pic (3).png", "isVideo": true},
          ],
        },
      ];
    } finally {
      isLoading(false);
    }
  }
}