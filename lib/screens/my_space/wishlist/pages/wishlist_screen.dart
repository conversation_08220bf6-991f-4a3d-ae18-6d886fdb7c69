import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../utils/utils.dart';
import '../controller/wishlist_controller.dart';

class WishlistScreen extends StatelessWidget {
  WishlistScreen({super.key});
  final WishlistController controller = Get.put(WishlistController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor.fromHex("AAAAAA"),
      appBar: AppBar(
        title: Text(
          "Wishlist",
          style: TextStyle(
            color: HexColor("555555"),
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        ),
        leading: Icon<PERSON><PERSON>on(
          icon: Icon(Icons.arrow_back_ios, color: HexColor("555555")),
          //onPressed: () => Get.back(),
          onPressed: (){
            Navigator.pop(context);
          },
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
      ),
      body: Column(
        children: [
          Container(
            height: 40,
            color: HexColor.fromHex("555555"),
          ),
          Expanded(
            child: Obx(() {
              return ListView(
                padding: const EdgeInsets.all(10),
                children: controller.wishlist
                    .map((category) => _buildCategoryBox(category))
                    .toList(),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBox(Map<String, dynamic> category) {
    return Container(
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: category["items"].length,
              itemBuilder: (context, index) {
                return _buildImageItem(
                  category["items"][index],
                  index,
                  category["items"].length,
                );
              },
            ),
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Text(
              category["category"],
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: HexColor.fromHex("555555"),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageItem(Map<String, dynamic> item, int index, int length) {
    BorderRadius radius = BorderRadius.only(
      topLeft: index == 0 ? Radius.circular(6) : Radius.zero,
      topRight: index == length - 1 ? Radius.circular(6) : Radius.zero,
    );

    return Stack(
      children: [
        Container(
          width: 90,
          margin: const EdgeInsets.only(right: 1),
          child: ClipRRect(
            borderRadius: radius,
            child: Image.asset(
              item["image"],
              width: 90,
              height: 100,
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: 6,
          right: 6,
          child: Icon(
            item["type"] == "video" ? Icons.videocam : Icons.crop_portrait_sharp,
            color: Colors.white,
            size: 16,
          ),
        ),
      ],
    );
  }
}
