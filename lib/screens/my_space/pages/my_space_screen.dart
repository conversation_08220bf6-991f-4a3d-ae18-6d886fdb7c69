import 'package:flutter/material.dart';

import '../../../constants/assets.dart';
import '../../../constants/colors.dart';
import '../../../utils/utils.dart';
import '../../notifications/pages/notification_screen.dart';
import '../suggestion_feature/pages/suggestion_screen.dart';
import '../wishlist/pages/wishlist_screen.dart';

class MySpaceScreen extends StatefulWidget {
  const MySpaceScreen({super.key});

  @override
  State<MySpaceScreen> createState() => _MySpaceScreenState();
}

class _MySpaceScreenState extends State<MySpaceScreen> {
  @override
  Widget build(BuildContext context) {
    final Size screenSize = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: ColorConstants.appColor,
      /*appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: ColorConstants.appColor,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        backgroundColor: ColorConstants.statusBarMySpaceColor,
        toolbarHeight: 0,
        elevation: 0,
      ),*/
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: Utils.pngImageAsset(AssetStrings.mySpaceImage),
            fit: BoxFit.cover, // Adjusts the image to cover the entire screen
          ),
        ),
        child: Stack(
          children: [
            //Utils.pngImage(AssetStrings.MySpaceImage, height: height * 1.0, width: 1.0 * width),
            // Top Right Circle
            Positioned(
              top: screenSize.height * 0.11, // 10% from the top
              right: screenSize.width * 0.11,
              child: GestureDetector(
                onTap: () {
                  // Navigate to the desired screen
                  Navigator.push(context, MaterialPageRoute(builder: (context) => SuggestionScreen()));
                },
                child: Container(
                  height: 125,
                  width: 125,
                  child: Utils.svgImage(AssetStrings.mySpaceCircleOneIcon),
                ),
              ),
            ),
            // Top Center Circle
            Positioned(
              top: screenSize.height * 0.22, // 20% from the top
              left: screenSize.width * 0.11, // 20% from the left
              child: GestureDetector(
                onTap: () {
                  // Navigate to the desired screen
                  Navigator.push(context, MaterialPageRoute(builder: (context) => NotificationScreen()));
                },
                child: Container(
                  height: 150,
                  width: 150,
                  child: Utils.svgImage(AssetStrings.mySpaceCircleTwoIcon),
                ),
              ),
            ),
            // Bottom Right Circle
            Positioned(
              top: screenSize.height * 0.45,
              right: screenSize.width * 0.45, // 30% from the right
              child: GestureDetector(
                onTap: () {
                  // Navigate to the desired screen
                  Navigator.push(context, MaterialPageRoute(builder: (context) => WishlistScreen()));
                },
                child: Container(
                  height: 125,
                  width: 125,
                  child: Utils.svgImage(AssetStrings.mySpaceCircleThreeIcon),
                ),
              ),
            ),
            // Bottom Center Circle
            Positioned(
              bottom: screenSize.height * 0.30, // 25% from the bottom
              left: screenSize.width * 0.65, // 50% from the left
              child: GestureDetector(
                onTap: () {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => NewScreen4()));
                },
                child: Container(
                  height: 100,
                  width: 100,
                  child: Utils.svgImage(AssetStrings.mySpaceCircleFourIcon),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}



class NewScreen4 extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('New Screen 4')),
      body: Center(child: Text('This is New Screen 4')),
    );
  }
}