import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/utils/utils.dart';
import '../../../../constants/assets.dart';
import '../controller/suggestion_controller.dart';

class SuggestionScreen extends StatefulWidget {
  const SuggestionScreen({super.key});

  @override
  State<SuggestionScreen> createState() => _SuggestionScreenState();
}

class _SuggestionScreenState extends State<SuggestionScreen> {
  final SuggestionController controller = Get.put(SuggestionController());

  void _showBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(25),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              const Text("Suggest an idea",
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 15)),
              const SizedBox(height: 10),
              const Text("What would make OpenSlate more useful to you?",style: TextStyle(fontSize: 13),),
              const SizedBox(height: 20),
              TextField(
                controller: controller.titleController,
                decoration: const InputDecoration(
                  hintText: "Title",
                  border: OutlineInputBorder(
                  ),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: controller.descriptionController,
                maxLines: 5,
                decoration: const InputDecoration(
                  hintText: "Description",
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 20),
              InkWell(
                onTap: () {
                  controller.addSuggestion();
                  Get.back();
                },
                child: Container(
                  width: double.infinity,  // equivalent to minimumSize.width
                  height: 50,              // equivalent to minimumSize.height
                  decoration: BoxDecoration(
                    color: HexColor("383838"),  // background color
                    borderRadius: BorderRadius.circular(4.0),  // default button shape
                    // Optional: Add shadow for elevation effect
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 2,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  alignment: Alignment.center,  // centers the child
                  child: const Text(
                    "Submit",
                    style: TextStyle(
                      color: Colors.white,  // text color
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case "PLANNED":
        return HexColor("FF5A80");
      case "IN REVIEW":
        return HexColor("F9C54B");
      case "IN DEV":
        return HexColor("5B7DF4");
      case "COMPLETED":
        return HexColor("17B26A");
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            "Suggestions",
            style: TextStyle(
              color: HexColor("555555"),
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios, color: HexColor("555555")),
            //onPressed: () => Get.back(),
            onPressed: (){
              Navigator.pop(context);
            },
          ),
          backgroundColor: Colors.grey.shade300,
          elevation: 0,
          centerTitle: false,
        ),
      backgroundColor: Colors.grey.shade300,
      body: Column(
        children: [
          Container(
            height: 40,
            color: HexColor.fromHex("555555"),
          ),
          Expanded(
            child: Obx(() => ListView.builder(
              padding: const EdgeInsets.all(5),
              itemCount: controller.suggestions.length,
              itemBuilder: (context, index) {
                final item = controller.suggestions[index];
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(Icons.calendar_today), // Calendar icon
                        SizedBox(width: 12), // spacing between icon and content
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                item['title'],
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w400,
                                  color: HexColor("0C0C0C"),
                                ),
                              ),
                              SizedBox(height: 1),
                              Row(
                                children: [
                                  Icon(Icons.circle,size: 12, color: _getStatusColor(item['status']),),
                                  SizedBox(width: 5,),
                                  Text(
                                    item['status'],
                                    style: TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.w400,
                                      color: HexColor("0C0C0C"),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 4),
                              Text(
                                item['description'],
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w400,
                                  color: HexColor("383838"),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 12), // spacing between content and message icon
                        Utils.pngImage(AssetStrings.messageIcon), // Message icon
                      ],
                    ),
                  ),
                );
                },
            )),
          ),
        ],
      ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                onTap: () => _showBottomSheet(context),
                child: Container(
                  decoration: BoxDecoration(
                    color: HexColor("#70D9FC"),
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16.0,
                    vertical: 12.0,
                  ),
                  child: Text(
                    "Suggest an Idea",
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: HexColor("#383838"),
                    ),
                  ),
                ),
              ),
            ],
          ),
        )
    );
  }
}
