import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SuggestionController extends GetxController {
  final titleController = TextEditingController();
  final descriptionController = TextEditingController();

  RxList<Map<String, dynamic>> suggestions = <Map<String, dynamic>>[
    {
      'title': 'Formatting Podcast Descriptions',
      'description': 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.',
      'status': 'PLANNED',
    },
    {
      'title': 'New Player Controls',
      'description': 'Lorem ipsum dolor sit amet, consectetuer adipiscing elit. Aenean commodo ligula eget dolor. Aenean massa. Cum sociis natoque penatibus et magnis dis parturient montes, nascetur ridiculus mus.',
      'status': 'IN REVIEW',
    },
  ].obs;

  void addSuggestion() {
    final title = titleController.text.trim();
    final description = descriptionController.text.trim();

    if (title.isNotEmpty) {
      suggestions.insert(0, {
        'title': title,
        'description': description,
        'status': 'PLANNED',
      });
      clearForm();
    }
  }

  void clearForm() {
    titleController.clear();
    descriptionController.clear();
  }

  @override
  void onClose() {
    titleController.dispose();
    descriptionController.dispose();
    super.onClose();
  }
}
