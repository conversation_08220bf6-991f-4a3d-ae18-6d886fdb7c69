import 'package:get/get.dart';
import '../../../models/project_model.dart';


class DesignerListController extends GetxController {
  RxList<String> categories = [
    '#-9',
    ...List.generate(26, (index) => String.fromCharCode(65 + index)), // A to Z
    'Following',
  ].obs;

  var selectedCategories = <String>[].obs;
  RxBool showStickyHorizontalChips = false.obs;

  @override
  void onInit() {
    super.onInit();

  }

  void updateStickyChipVisibility(double offset) {
    if (offset > 200 && !showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = true;
    } else if (offset <= 200 && showStickyHorizontalChips.value) {
      showStickyHorizontalChips.value = false;
    }
  }
}
