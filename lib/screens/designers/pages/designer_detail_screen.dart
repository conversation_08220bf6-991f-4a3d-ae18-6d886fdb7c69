import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';


class DesignerDetailScreen extends StatelessWidget {
  const DesignerDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final tiles = [
      _designerCard(),
      _aboutCard(),
      _awardAndLikesCard(),
      _quoteCard(),
      _foundersCard(),
      _projectCard(),
      _collectionCard(),
      _postCard(),
      _contactCard(),
    ];

    return Scaffold(
      appBar: AppBar(
        title: const Text("DESIGNERS > MuseLAB", style: TextStyle(color: Colors.black, fontSize: 16)),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: BackButton(color: Colors.black),
      ),
      body: MasonryGridView.count(
        crossAxisCount: 2,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        padding: const EdgeInsets.all(12),
        itemCount: tiles.length,
        itemBuilder: (context, index) => tiles[index],
      ),
    );
  }

  // ---------------- Cards ----------------

  Widget _designerCard() {
    return Container(
      decoration: _tileDecoration(Colors.black),
      padding: const EdgeInsets.all(12),
      child: Column(
        children: [
          const CircleAvatar(
            radius: 35,
            backgroundColor: Colors.white,
            backgroundImage: NetworkImage("https://upload.wikimedia.org/wikipedia/commons/thumb/1/17/MuseLAB_logo.svg/320px-MuseLAB_logo.svg.png"),
          ),
          const SizedBox(height: 10),
          const Text("MuseLAB", style: TextStyle(color: Colors.white)),
          const SizedBox(height: 8),
          ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey[800],
              minimumSize: const Size.fromHeight(35),
            ),
            child: const Text("Follow"),
          ),
          const SizedBox(height: 6),
          const Align(
            alignment: Alignment.topRight,
            child: Icon(Icons.star, color: Colors.yellow, size: 20),
          )
        ],
      ),
    );
  }

  Widget _aboutCard() {
    return Container(
      height: 80,
      decoration: _tileDecoration(Colors.yellow),
      child: const Padding(
        padding: EdgeInsets.all(10),
        child: Align(
          alignment: Alignment.centerLeft,
          child: Text("About", style: TextStyle(fontSize: 16)),
        ),
      ),
    );
  }

  Widget _awardAndLikesCard() {
    return Column(
      children: [
        Row(
          children: [
            _infoTile("9+", "Awards"),
            const SizedBox(width: 8),
            _infoTile("31k", "OpenLikes"),
          ],
        ),
      ],
    );
  }

  Widget _infoTile(String title, String subtitle) {
    return Expanded(
      child: Container(
        height: 60,
        decoration: _tileDecoration(Colors.grey[300]),
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            Text(subtitle, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Widget _quoteCard() {
    return Container(
      height: 100,
      decoration: _tileDecoration(Colors.lightBlueAccent),
      padding: const EdgeInsets.all(12),
      child: const Center(
        child: Text(
          "“Good design is obvious. Great design is transparent.”",
          textAlign: TextAlign.center,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _foundersCard() {
    return Container(
      height: 100,
      decoration: _tileDecoration(Colors.grey[850]),
      padding: const EdgeInsets.all(8),
      child: Row(
        children: [
          _personTile("https://randomuser.me/api/portraits/men/12.jpg", "JASEM\nPIRANI"),
          const SizedBox(width: 10),
          _personTile("https://randomuser.me/api/portraits/men/13.jpg", "HUZEFA\nRANGWALA"),
        ],
      ),
    );
  }

  Widget _personTile(String img, String name) {
    return Column(
      children: [
        CircleAvatar(backgroundImage: NetworkImage(img), radius: 25),
        const SizedBox(height: 5),
        Text(name, style: const TextStyle(color: Colors.white, fontSize: 10), textAlign: TextAlign.center),
      ],
    );
  }

  Widget _projectCard() {
    return _imageTile("Projects", "12+", "https://source.unsplash.com/random/401x300/?furniture");
  }

  Widget _collectionCard() {
    return _imageTile("Collections", "19+", "https://source.unsplash.com/random/402x300/?interior");
  }

  Widget _postCard() {
    return _imageTile("Posts", "52+", "https://source.unsplash.com/random/403x300/?design");
  }

  Widget _contactCard() {
    return Container(
      height: 80,
      decoration: _tileDecoration(Colors.grey[400]),
      padding: const EdgeInsets.all(10),
      alignment: Alignment.centerLeft,
      child: const Text("Contact", style: TextStyle(fontSize: 16)),
    );
  }

  Widget _imageTile(String tag, String count, String img) {
    return Container(
      height: 140,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        image: DecorationImage(image: NetworkImage(img), fit: BoxFit.cover),
      ),
      child: Stack(
        children: [
          Positioned(
            left: 8,
            top: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              color: Colors.white70,
              child: Text(count, style: const TextStyle(fontWeight: FontWeight.bold)),
            ),
          ),
          Positioned(
            bottom: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              color: Colors.black54,
              child: Text(tag, style: const TextStyle(color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }

  BoxDecoration _tileDecoration(Color? color) {
    return BoxDecoration(
      color: color ?? Colors.grey[300],
      borderRadius: BorderRadius.circular(12),
    );
  }
}
