import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../common/widgets/cached_image_network_widget.dart';
import '../../../constants/assets.dart';
import '../../bottom_navigation/controller/bottom_navigation_controller.dart';
import '../controller/designer_list_controller.dart';

class DesignerListScreen extends StatefulWidget {
  @override
  State<DesignerListScreen> createState() => _DesignerListScreenState();
}

class _DesignerListScreenState extends State<DesignerListScreen> {
  final DesignerListController controller = Get.put(DesignerListController());
  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<bool> showStickyHeader = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Show sticky header when scrolled past the expanded app bar
    final shouldShow = _scrollController.offset > 200;
    if (showStickyHeader.value != shouldShow) {
      showStickyHeader.value = shouldShow;
    }
  }

  Future<bool> _onWillPop() async {
    final navController = Get.find<BottomNavigationController>();
    final currentTabIndex = navController.selectedIndex.value;

    if (navController.navigatorKeys[currentTabIndex].currentState?.canPop() ?? false) {
      navController.navigatorKeys[currentTabIndex].currentState?.pop();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final currentTabIndex = Get.find<BottomNavigationController>().selectedIndex.value;
    final currentTabNavigatorId = currentTabIndex + 1;

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Colors.blue.shade50,
        body: Stack(
          children: [
            CustomScrollView(
              controller: _scrollController,
              slivers: [
                SliverAppBar(
                  leading: IconButton(
                    onPressed: () {
                      Get.back(id: currentTabNavigatorId);
                    },
                    icon: const Icon(Icons.arrow_back_ios),
                  ),
                  title: const Text(
                    "Designers",
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  actions: [
                    // Show Designer button in app bar when scrolled
                    ValueListenableBuilder<bool>(
                      valueListenable: showStickyHeader,
                      builder: (context, showSticky, child) {
                        return showSticky
                            ? Container(
                                margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
                                child: _buildRectangleDesignerButton(),
                              )
                            : const SizedBox.shrink();
                      },
                    ),
                  ],
                  expandedHeight: 280,
                  pinned: true,
                  flexibleSpace: FlexibleSpaceBar(
                    collapseMode: CollapseMode.parallax,
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Positioned.fill(
                          child: Image.asset(
                            AssetStrings.projectsBgImage,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          ),
                        ),
                        Positioned.fill(
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                            child: Container(color: Colors.black.withValues(alpha: 0.0)),
                          ),
                        ),
                        SingleChildScrollView(
                          padding: const EdgeInsets.only(top: 100),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 12),
                            child: Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              alignment: WrapAlignment.start,
                              children: [
                                ...['#-9', ...List.generate(26, (i) => String.fromCharCode(65 + i)), 'Following'].map(
                                      (letter) => Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(30),
                                      border: Border.all(color: Colors.black),
                                    ),
                                    child: Text(
                                      letter,
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // 👉 Designer of the Day inserted below SliverAppBar
                SliverToBoxAdapter(
                  child: _buildDesignerOfTheDaySection(),
                ),
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                      return _buildAdWidget();
                    },
                    childCount: 4,
                  ),
                ),
              ],
            ),
            // Round "Join as Designer" button - visible when not scrolled
            ValueListenableBuilder<bool>(
              valueListenable: showStickyHeader,
              builder: (context, showSticky, child) {
                return AnimatedPositioned(
                  duration: const Duration(milliseconds: 200),
                  top: 70,
                  right: showSticky ? -120 : 16, // Hide when sticky header shows
                  child: AnimatedOpacity(
                    duration: const Duration(milliseconds: 200),
                    opacity: showSticky ? 0.0 : 1.0,
                    child: GestureDetector(
                      onTap: () {
                        print('Join as Designer!');
                      },
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.yellow,
                          shape: BoxShape.circle,
                          image: DecorationImage(
                            image: AssetImage(AssetStrings.projectsBgImage),
                            fit: BoxFit.cover,
                            colorFilter: ColorFilter.mode(
                              Colors.yellow.withValues(alpha: 0.8),
                              BlendMode.overlay,
                            ),
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        alignment: Alignment.center,
                        padding: const EdgeInsets.all(8),
                        child: const Text(
                          'Join as Designer!',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.black87,
                            fontWeight: FontWeight.bold,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
            // Sticky row component with A-Z, 0-9, Following buttons
            ValueListenableBuilder<bool>(
              valueListenable: showStickyHeader,
              builder: (context, showSticky, child) {
                return AnimatedPositioned(
                  duration: const Duration(milliseconds: 200),
                  top: showSticky ? kToolbarHeight + MediaQuery.of(context).padding.top : -60,
                  left: 0,
                  right: 0,
                  child: Container(
                    height: 50,
                    //color: Colors.white,
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStickyButton('A-Z', () {
                            print('A-Z button tapped');
                          }),
                        ),
                        Container(width: 1, height: 30, color: Colors.grey.shade300),
                        Expanded(
                          child: _buildStickyButton('0-9', () {
                            print('0-9 button tapped');
                          }),
                        ),
                        Container(width: 1, height: 30, color: Colors.grey.shade300),
                        Expanded(
                          child: _buildStickyButton('Following', () {
                            print('Following button tapped');
                          }),
                        ),
                        Container(width: 1, height: 30, color: Colors.grey.shade300),
                        _buildIconButton(Icons.search, () {
                          print('Search button tapped');
                        }),
                        Container(width: 1, height: 30, color: Colors.grey.shade300),
                        _buildIconButton(Icons.filter_list, () {
                          print('Filter button tapped');
                        }),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdWidget() {
    return Container(
      color: Colors.white,
      height: 190,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text.rich(
            TextSpan(
              children: [
                TextSpan(
                  text: "Posts : ",
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                TextSpan(
                  text: "Catchup on the updates",
                  style: TextStyle(
                    color: Colors.black54,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Container(
            color: Colors.black,
            child: SizedBox(
              height: 140,
              child: ListView.builder(
                itemCount: 4,
                scrollDirection: Axis.horizontal,
                itemBuilder: (context, adIndex) {
                  final studioNames = [
                    "Purple Studio",
                    "quirk studio",
                    "Ceric Studio",
                    "Muse Studio",
                    "Core Studio",
                  ];
                  final studioColors = [
                    Colors.deepOrange,
                    Colors.blue,
                    Colors.yellow,
                    Colors.pinkAccent,
                    Colors.green,
                  ];

                  return Padding(
                    padding: EdgeInsets.only(right: adIndex == 3 ? 0 : 12.0),
                    child: Container(
                      width: 140,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      clipBehavior: Clip.antiAlias,
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: CachedImageWidget(
                                imageUrl: 'https://picsum.photos/id/10${adIndex + 1}/600/300',
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                colors: [Colors.black.withOpacity(0.3), Colors.transparent],
                                begin: Alignment.bottomCenter,
                                end: Alignment.topCenter,
                                stops: const [0.0, 0.5],
                              ),
                            ),
                          ),
                          Positioned(
                            top: 8,
                            left: 8,
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 8,
                                  backgroundColor: studioColors[adIndex % studioColors.length],
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  studioNames[adIndex],
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesignerOfTheDaySection() {
    return Container(
      color: Colors.yellow,
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: const [
              Text(
                "DESIGNER OF THE DAY",
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.black,
                ),
              ),
              Icon(Icons.star, color: Colors.black, size: 20),
            ],
          ),

          const SizedBox(height: 16),

          // Main Content Row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Left Info
              Container(
                width: 180,
                color: Colors.black87,
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    CircleAvatar(
                      radius: 30,
                      backgroundImage: NetworkImage('https://picsum.photos/seed/muselab/150/150'),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "MuseLAB",
                      style: TextStyle(color: Colors.white, fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      "Mumbai",
                      style: TextStyle(color: Colors.white60, fontSize: 14),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "5 Projects    34 Posts    9 Awards",
                      style: TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                    SizedBox(height: 8),
                    Text(
                      "HUZEFA RANGWALA\nPrinciple Architect\n\nJASEEM PIRANI\nPrinciple Architect",
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),


              // Right images
              Expanded(
                child: Column(
                  children: [
                    Container(
                      height: 178,
                      color: Colors.black,
                    ),
                    Row(
                      children:  [
                        Expanded(
                          child: CachedImageWidget(
                            imageUrl: 'https://picsum.photos/id/1011/100/100',
                            fit: BoxFit.cover,
                          ),
                        ),
                        Expanded(
                          child: CachedImageWidget(
                            imageUrl: 'https://picsum.photos/id/1012/100/100',
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStickyButton(String text, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AssetStrings.projectsBgImage),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.white.withValues(alpha: 0.9),
              BlendMode.overlay,
            ),
          ),
        ),
        child: Text(
          text,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

  Widget _buildIconButton(IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 50,
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(AssetStrings.projectsBgImage),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.white.withValues(alpha: 0.9),
              BlendMode.overlay,
            ),
          ),
        ),
        child: Icon(
          icon,
          color: Colors.black87,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildRectangleDesignerButton() {
    return InkWell(
      onTap: () {
        print('Rectangle Designer button tapped');
      },
      child: Container(
        width: 80,
        height: 50,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: Colors.yellow,
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: AssetImage(AssetStrings.projectsBgImage),
            fit: BoxFit.cover,
            colorFilter: ColorFilter.mode(
              Colors.yellow.withValues(alpha: 0.8),
              BlendMode.overlay,
            ),
          ),
        ),
        child: const Text(
          'Designer',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      ),
    );
  }

}
