import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/routes.dart';
import '../../../utils/utils.dart';
import '../controller/sign_up_controller.dart';

class ProfileSetupScreen extends StatelessWidget {
  final SignUpController controller = Get.put(SignUpController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Centered Basic Info Heading
              const Center(
                child: Text(
                  "Basic info",
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(height: 30),

              // Age and Gender in a row
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: 'Age',
                        focusedBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.grey.shade800), // dark grey
                          borderRadius: BorderRadius.circular(8),
                        ),
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                      ),
                      onChanged: (val) => controller.age.value = val,
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Obx(() {
                      return DropdownButtonFormField<String>(
                        value: controller.selectedGender.value.isEmpty
                            ? null
                            : controller.selectedGender.value,
                        decoration: InputDecoration(
                          labelText: 'Gender',
                          focusedBorder: OutlineInputBorder(
                            borderSide: BorderSide(color: Colors.grey.shade800), // dark grey
                            borderRadius: BorderRadius.circular(8),
                          ),
                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                        ),
                        items: controller.genderOptions.map((String gender) {
                          return DropdownMenuItem<String>(
                            value: gender,
                            child: Text(gender),
                          );
                        }).toList(),
                        onChanged: (val) {
                          if (val != null) {
                            controller.selectedGender.value = val;
                          }
                        },
                      );
                    }),
                  ),
                ],
              ),
              const SizedBox(height: 10),

              // Phone Number
              TextField(
                keyboardType: TextInputType.phone,
                decoration: InputDecoration(
                  labelText: 'Phone no',
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade800), // dark grey
                    borderRadius: BorderRadius.circular(8),
                  ),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                ),
                onChanged: (val) => controller.phoneNumber.value = val,
              ),
              const SizedBox(height: 10),

              // Location
              TextField(
                decoration: InputDecoration(
                  labelText: 'Location',
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade800), // dark grey
                    borderRadius: BorderRadius.circular(8),
                  ),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
                ),
                onChanged: (val) => controller.location.value = val,
              ),

              const SizedBox(height: 30),
              const Text("Choose your role", style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
              const SizedBox(height: 15),

              // Roles with updated style
              Obx(() => Wrap(
                spacing: 4,
                runSpacing: 4,
                children: controller.roleOptions.map((role) {
                  final bool isSelected = controller.selectedRole.value == role;
                  return GestureDetector(
                    onTap: () => controller.selectedRole.value = role,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: isSelected ? HexColor("6F6F6F") : Colors.transparent,
                        border: Border.all(color: isSelected ? HexColor("6F6F6F") : Colors.grey),
                        borderRadius: BorderRadius.circular(30),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (isSelected)
                            const Icon(Icons.check, size: 14, color: Colors.white),
                          if (isSelected)
                            const SizedBox(width: 4),
                          Text(
                            role,
                            style: TextStyle(
                              fontSize: 10,
                              color: isSelected ? Colors.white : Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              )),

              Spacer(),

             Divider(),
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => Get.back(),
                      child: Text('Back', style: TextStyle(color: Colors.black)),
                    ),),
                  const SizedBox(width: 10),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {

                        Get.toNamed(Routes.bottomNavigationScreen, arguments: {'index': 1});
                        print("Age: ${controller.age.value}");
                        print("Gender: ${controller.selectedGender.value}");
                        print("Phone: ${controller.phoneNumber.value}");
                        print("Role: ${controller.selectedRole.value}");
                      }, child: Text( 'Continue', style: TextStyle(color: Colors.white)),
                      style: ElevatedButton.styleFrom(
                          backgroundColor: HexColor("383838"),
                          minimumSize: Size(double.infinity, 45),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6),
                          ),
                      ),
                  ),
                  )],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
