import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/utils/utils.dart';
import '../controller/sign_up_controller.dart';
import 'basic_info_profile_screen.dart';

class UserNameScreen extends StatefulWidget {
  @override
  _UserNameScreenState createState() => _UserNameScreenState();
}

class _UserNameScreenState extends State<UserNameScreen> {
  final controller = Get.find<SignUpController>();
  late TextEditingController _usernameController;

  @override
  void initState() {
    super.initState();
    _usernameController = TextEditingController(text: controller.username.value);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 40),
              const Center(
                child: Text(
                  "Choose a username",
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                "This helps identify you without using your personal info",
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 50),
              TextField(
                controller: _usernameController,
                onChanged: (val) {
                  controller.username.value = val;
                  controller.checkUsernameAvailability(val);
                },
                cursorColor: Colors.black,
                style: const TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                ),
                decoration: const InputDecoration(
                  hintText: "Username",
                  border: InputBorder.none,
                  isCollapsed: true,
                ),
              ),
              const SizedBox(height: 10),

              // Username availability status
              Obx(() {
                if (controller.username.value.isEmpty) return const SizedBox();
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      )
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        controller.isUsernameValid.value ? Icons.check_circle : Icons.cancel,
                        size: 14,
                        color: controller.isUsernameValid.value ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        controller.isUsernameValid.value
                            ? 'Username is available'
                            : 'At least 3 characters, no spaces',
                        style: TextStyle(
                          fontSize: 12,
                          color: HexColor("555555"),
                        ),
                      ),
                    ],
                  ),
                );
              }),

              const Spacer(),
              const Divider(),
              // Bottom Buttons
              Row(
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () => Get.back(),
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 14),
                        child: Text('Back', textAlign: TextAlign.left),
                      ),
                    ),
                  ),
                  const SizedBox(width: 10),
                  Expanded(
                    child: Obx(() => ElevatedButton(
                      onPressed: controller.isUsernameValid.value
                          ? () {
                        FocusScope.of(context).unfocus();
                        Get.to(() => ProfileSetupScreen());
                      }
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: HexColor("383838"),
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Continue 2/3',
                        style: TextStyle(color: Colors.white),
                      ),
                    )),
                  ),
                ],
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }
}