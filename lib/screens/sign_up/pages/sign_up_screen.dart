import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/sign_up/pages/user_name_screen.dart';
import 'package:openslate/utils/utils.dart';
import '../controller/sign_up_controller.dart';

class SignUpScreen extends StatefulWidget {
  @override
  _SignUpScreenState createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final controller = Get.find<SignUpController>();

  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController = TextEditingController();

  bool _validated = false;
  bool _obscurePassword = true;
  bool _obscureConfirmPassword = true;

  @override
  void initState() {
    super.initState();
    passwordController.addListener(() {
      setState(() {
        _validated = true;
      });
    });
  }

  bool _validatePassword(String password) {
    return password.length >= 8 &&
        password.contains(RegExp(r'[A-Z]')) &&
        password.contains(RegExp(r'[a-z]')) &&
        password.contains(RegExp(r'[0-9!@#\$&*~]'));
  }

  bool _isPasswordMatch() {
    return passwordController.text == confirmPasswordController.text;
  }

  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w]{2,4}').hasMatch(email);
  }

  bool get _isFormValid {
    return _isValidEmail(emailController.text) &&
        _validatePassword(passwordController.text) &&
        _isPasswordMatch();
  }

  @override
  void dispose() {
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Sign Up'),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          children: [
            _commonTextField(
              controller: emailController,
              label: "Email",
              isPassword: false,
              obscureText: false,
              onChanged: (val) => setState(() {}),
            ),
            SizedBox(height: 15),
            _commonTextField(
              controller: passwordController,
              label: "Password",
              isPassword: true,
              obscureText: _obscurePassword,
              onToggleVisibility: () {
                setState(() {
                  _obscurePassword = !_obscurePassword;
                });
              },
              onChanged: (val) => setState(() {}),
            ),
            SizedBox(height: 15),
            _commonTextField(
              controller: confirmPasswordController,
              label: "Confirm Password",
              isPassword: true,
              obscureText: _obscureConfirmPassword,
              onToggleVisibility: () {
                setState(() {
                  _obscureConfirmPassword = !_obscureConfirmPassword;
                });
              },
              onChanged: (val) => setState(() {}),
            ),
            SizedBox(height: 20),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildValidationRow(_validated, passwordController.text.length >= 8, 'At least 8 characters'),
                _buildValidationRow(_validated,
                    passwordController.text.contains(RegExp(r'[A-Z]')) &&
                        passwordController.text.contains(RegExp(r'[a-z]')),
                    'One uppercase and one lowercase letter'),
                _buildValidationRow(_validated,
                    passwordController.text.contains(RegExp(r'[0-9!@#\$&*~]')),
                    'Contains number or special character'),
                _buildValidationRow(_validated, _isPasswordMatch(), 'Passwords match'),
              ],
            ),
            Spacer(),
            Divider(),
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => Get.back(),
                    child: Text('Cancel', style: TextStyle(color: Colors.black)),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isFormValid
                        ? () {
                      controller.email.value = emailController.text;
                      controller.password.value = passwordController.text;
                      controller.confirmPassword.value = confirmPasswordController.text;
                      controller.signUp(); // You can change this to `signUp()` if you add that method.
                      Get.to(() => UserNameScreen());
                    }
                        : null,
                    child: Text( 'Continue 1/3', style: TextStyle(color: Colors.white)),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: HexColor("383838"),
                      minimumSize: Size(double.infinity, 45),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _commonTextField({
    required TextEditingController controller,
    required String label,
    required bool isPassword,
    required bool obscureText,
    required Function(String) onChanged,
    VoidCallback? onToggleVisibility,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      onChanged: onChanged,
      cursorColor: HexColor("6F6F6F"),
      style: TextStyle(fontSize: 14),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(fontSize: 12, color: HexColor("555555")),
        isDense: true,
        contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: HexColor("D8D8D8")),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6),
          borderSide: BorderSide(color: HexColor("6F6F6F")),
        ),
        suffixIcon: isPassword
            ? IconButton(
          icon: Icon(
            obscureText ? Icons.visibility_off_outlined : Icons.visibility_outlined,
            color: HexColor('6F6F6F'),
            size: 20,
          ),
          onPressed: onToggleVisibility,
        )
            : null,
      ),
    );
  }

  Widget _buildValidationRow(bool validated, bool condition, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            !validated
                ? Icons.warning_amber_rounded
                : (condition ? Icons.check_circle : Icons.cancel),
            color: !validated
                ? HexColor("6F6F6F")
                : (condition ? Colors.green : Colors.red),
            size: 18,
          ),
          SizedBox(width: 8),
          Text(text, style: TextStyle(fontSize: 12, color: HexColor('959595'))),
        ],
      ),
    );
  }
}
