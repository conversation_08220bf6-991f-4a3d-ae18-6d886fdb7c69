import 'package:get/get.dart';

class SignUpController extends GetxController {
  var email = ''.obs;
  var password = ''.obs;
  var confirmPassword = ''.obs;
  var otpCode = ''.obs;
  var isPasswordVisible = false.obs;
  var isConfirmPasswordVisible = false.obs;
  var username = ''.obs;
  var isUsernameValid = false.obs;

  var selectedGender = ''.obs;
  var selectedRole = ''.obs;
  var age = ''.obs;
  var phoneNumber = ''.obs;
  var location = ''.obs;

  final List<String> genderOptions = ['Male', 'Female', 'Other'];
  final List<String> roleOptions = ['Developer', 'Designer', 'Manager', 'Tester'];


  void checkUsernameAvailability(String value) {
    username.value = value;
    isUsernameValid.value = value.trim().length >= 5 && !value.contains(' ');
  }

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  void signUp() {
    // TODO: Integrate your API here
  }

  void sendResetLink() {
    // TODO: Integrate your API here
  }

  void resetPassword() {
    // TODO: Integrate your API here
  }
}
