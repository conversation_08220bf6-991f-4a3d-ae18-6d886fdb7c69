import 'package:get/get.dart';

class NotificationController extends GetxController {
  var notifications = <Map<String, String>>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
  }

  void loadNotifications() {
    notifications.value = [
      {
        "userName": "<PERSON>",
        "userImage": "https://randomuser.me/api/portraits/men/1.jpg",
        "message": "liked your post Modern Architecture",
        "time": "5h",
        "postImage": "",
      },
      {
        "userName": "Sam",
        "userImage": "https://randomuser.me/api/portraits/men/2.jpg",
        "message": "liked your post Modern Architecture",
        "time": "5h",
        "postImage": "",
      },
      {
        "userName": "Sanz",
        "userImage": "https://randomuser.me/api/portraits/women/3.jpg",
        "message": "liked your post Modern Architecture",
        "time": "5h",
        "postImage": "",
      },
      {
        "userName": "<PERSON>",
        "userImage": "https://randomuser.me/api/portraits/women/4.jpg",
        "message": "liked your post Modern Architecture",
        "time": "5h",
        "postImage": "",
      },
    ];
  }
}
