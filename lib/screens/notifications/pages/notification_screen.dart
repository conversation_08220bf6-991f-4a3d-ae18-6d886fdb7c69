import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../constants/assets.dart';
import '../../../constants/colors.dart';
import '../../../utils/utils.dart';
import '../controller/notification_controller.dart';


class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final NotificationController controller = Get.put(NotificationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.appColor,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: ColorConstants.appColor,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
        ),
        backgroundColor: ColorConstants.appColor,
        toolbarHeight: 45,
        elevation: 0,
        leading: InkWell(
            onTap: (){
              Navigator.pop(context);
            },
            child: Icon(Icons.arrow_back_ios_outlined,color: ColorConstants.notificationColor,size: 30,)),
        title: Container(
            child: Text("NOTIFICATIONS",style: TextStyle(color: ColorConstants.notificationColor,fontSize: 18,fontWeight: FontWeight.w400),)),
      ),
      body: Obx(() {
        return Container(
          child: ListView.builder(
            padding: const EdgeInsets.all(10),
            itemCount: controller.notifications.length,
            itemBuilder: (context, index) {
              return _buildNotificationItem(controller.notifications[index]);
            },
          ),
        );
      }),
    );
  }

  Widget _buildNotificationItem(Map<String, String> notification) {
    return Column(
      children: [
        ListTile(
          leading: CircleAvatar(
            backgroundImage: NetworkImage(notification["userImage"]!),
            maxRadius: 25,
          ),
          title: RichText(
            text: TextSpan(
              style: TextStyle(color: ColorConstants.notificationTextColor),
              children: [
                TextSpan(
                  text: notification["userName"],
                  style: TextStyle(fontWeight: FontWeight.bold,fontSize: 13),
                ),
                TextSpan(text: " ${notification["message"]}",style: TextStyle(fontSize: 13)),
                TextSpan(text: " ${notification["time"]}",style: TextStyle(fontSize: 13,color: ColorConstants.notificationColor)),
              ],
            ),
          ),
          trailing: Utils.pngImage(AssetStrings.notificationListIcon,),
        ),
        Divider(color: ColorConstants.notificationDividerColor,),
      ],
    );
  }
}
