import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../constants/colors.dart';


class CategoryButton extends StatelessWidget {
  final String label;
  final bool highlight;
  final VoidCallback? onTap;

  const CategoryButton({
    super.key,
    required this.label,
    this.highlight = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    const borderRadius = BorderRadius.all(Radius.circular(40));

    return InkWell(
      onTap: onTap,
      borderRadius: borderRadius,
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: highlight ? ColorConstants.highlightedColor : ColorConstants.bottomNavigationColor,
          borderRadius: borderRadius,
        ),
        child: Center(
          child: SizedBox(
            width: 52,
            height: 15,
            child: Text(
              _toTitleCase(label),
              textAlign: TextAlign.center,
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w400,
                height: 1.0,
                letterSpacing: 0,
                color: ColorConstants.textCategoryColor,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Converts text to Title Case (each word capitalized)
  String _toTitleCase(String text) {
    return text
        .split(' ')
        .map((word) =>
    word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : '')
        .join(' ');
  }
}
