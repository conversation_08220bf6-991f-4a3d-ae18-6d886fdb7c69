import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../../common/widgets/cached_image_network_widget.dart';

class BuzzingCardWidget extends StatefulWidget {
  final List<String> imageList;
  final List<String> logoTexts;
  final List<String> logoInitials;
  final String title;
  final double width;
  final double height;
  final bool useCarousel;
  final bool autoPlayCarousel;

  const BuzzingCardWidget({
    super.key,
    required this.imageList,
    required this.logoTexts,
    required this.logoInitials,
    this.title = 'Buzzing',
    this.width = 120,
    this.height = 250,
    this.useCarousel = false,
    this.autoPlayCarousel = true,
  });

  @override
  State<BuzzingCardWidget> createState() => _BuzzingCardWidgetState();
}

class _BuzzingCardWidgetState extends State<BuzzingCardWidget> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    // Safety checks for empty lists or out of bounds index
    String logoInitial = widget.logoInitials.isNotEmpty && _currentIndex < widget.logoInitials.length
        ? widget.logoInitials[_currentIndex]
        : 'B'; // Default initial
    String logoText = widget.logoTexts.isNotEmpty && _currentIndex < widget.logoTexts.length
        ? widget.logoTexts[_currentIndex]
        : 'Buzzing'; // Default text

    Widget cardContent = Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: const Color(0xFFEDEDED), // Grey background - same as designers
        borderRadius: BorderRadius.circular(24), // Same as designers
        boxShadow: const [
          BoxShadow(
            color: Colors.black12, // Same as designers
            blurRadius: 6, // Same as designers
            offset: Offset(0, 3), // Same as designers
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // White container with carousel and title - 67% of height
          Container(
            height: widget.height * 0.67,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24), // Match grey container top curve
                topRight: Radius.circular(24), // Match grey container top curve
                 bottomLeft: Radius.circular(24),
                  bottomRight: Radius.circular(24),
              ),
            ),
            child: Column(
              children: [
                // Image section at top - 55% of height
                Container(
                  height: widget.height * 0.55,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20), // Curved on all sides
                    border: Border.all(
                      color: Colors.black.withValues(alpha: 0.4), // Black border - same as designers
                      width: 1.5, // Same as designers
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(18.5), // Curved on all sides, slightly smaller
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Carousel or single image
                        widget.useCarousel && widget.imageList.length > 1
                            ? CarouselSlider(
                                options: CarouselOptions(
                                  viewportFraction: 1.0,
                                  height: double.infinity,
                                  autoPlay: widget.autoPlayCarousel,
                                  autoPlayInterval: const Duration(seconds: 3),
                                  onPageChanged: (index, reason) {
                                    setState(() {
                                      // Ensure index is within bounds for both lists
                                      if (index < widget.imageList.length &&
                                          index < widget.logoInitials.length &&
                                          index < widget.logoTexts.length) {
                                        _currentIndex = index;
                                      }
                                    });
                                  },
                                ),
                                items: widget.imageList.map((imageUrl) {
                                  return CachedImageWidget(imageUrl: imageUrl);
                                }).toList(),
                              )
                            : widget.imageList.isNotEmpty
                                ? CachedImageWidget(imageUrl: widget.imageList[0])
                                : const Center(child: Icon(Icons.image, size: 40, color: Colors.grey)),
                        // Logo positioned at bottom left
                        Positioned(
                          bottom: 8,
                          left: 8,
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Logo initial in circle - same as designers card
                              Container(
                                width: 20,
                                height: 20,
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: Text(
                                    logoInitial,
                                    style: const TextStyle(
                                      color: Colors.redAccent, // Same as designers
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 4),
                              // Logo text - same styling as designers card
                              Flexible(
                                child: Text(
                                  logoText,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600, // Same as designers
                                    fontSize: 10, // Same as designers
                                    shadows: [
                                      Shadow(
                                        color: Colors.black54, // Same as designers
                                        blurRadius: 6, // Same as designers
                                        offset: Offset(1, 1), // Same as designers
                                      )
                                    ],
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                // Title section with white background - 12% of height
                Container(
                  height: widget.height * 0.12,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                     borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24), // Match grey container top curve
                bottomRight: Radius.circular(24), // Match grey container top curve
              ),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    widget.title,
                    style: const TextStyle(
                      fontSize: 16, // Same as designers
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      letterSpacing: 1.2, // Same as designers
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Empty grey space at bottom - 33% of height
          Container(
            height: widget.height * 0.33,
            decoration: const BoxDecoration(
              color: Color(0xFFEDEDED),
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(24),
                bottomRight: Radius.circular(24),
              ),
            ),
          ),
        ],
      ),
    );

    return cardContent;
  }
}
