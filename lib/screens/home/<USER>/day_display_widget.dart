import 'package:flutter/material.dart';

class DayDisplayWidget extends StatelessWidget {
  final String dayPrefix;
  final String daySuffix;
  final VoidCallback? onTapOutside;

  const DayDisplayWidget({
    super.key,
    required this.dayPrefix,
    required this.daySuffix,
    this.onTapOutside,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          dayPrefix,
          style: const TextStyle(
            fontSize: 30,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [Shadow(blurRadius: 10, color: Colors.black26)],
          ),
        ),
        const Text(
          'inspirations',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.normal,
            color: Colors.grey,
          ),
        ),
        Text(
          daySuffix,
          style: const TextStyle(
            fontSize: 30,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            shadows: [Shadow(blurRadius: 10, color: Colors.black26)],
          ),
        ),
      ],
    );
  }
}
