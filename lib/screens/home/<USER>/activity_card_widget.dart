import 'dart:math' as math;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../../common/widgets/cached_image_network_widget.dart';

class ActivityCard extends StatelessWidget {
  final List<String> imageList;
  final bool useCarousel;
  final bool autoPlayCarousel;
  final String title;
  final String logoText;
  final String logoInitial;
  final double width;
  final double height;
  final bool rotate;
  final double rotateAngleDegree; // in degrees, example: 270

  const ActivityCard({
    Key? key,
    required this.imageList,
    this.useCarousel = false,
    this.autoPlayCarousel = true,
    this.title = 'Designers',
    this.logoText = 'MuseLAB',
    this.logoInitial = 'M',
    this.width = 100,
    this.height = 200,
    this.rotate = false,
    this.rotateAngleDegree = 0.0, // by default no rotation
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget cardContent = Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 5,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Top section
          Container(
            decoration: const BoxDecoration(
              color: Color(0xFFEDEDED),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                const SizedBox(height: 40),
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 2),
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Image Section
          Expanded(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
                topRight: Radius.circular(16),
                topLeft: Radius.circular(16),
              ),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  useCarousel
                      ? Positioned.fill(
                        child: CarouselSlider(
                                            options: CarouselOptions(
                        viewportFraction: 1.0,
                        height: double.infinity,
                        autoPlay: autoPlayCarousel,
                                            ),
                                            items: imageList.map((imageUrl) {
                        return Transform.rotate(
                          angle: - rotateAngleDegree * (math.pi / 180),
                          child: CachedImageWidget(imageUrl: imageUrl),
                        );
                                            }).toList(),
                                          ),
                      )
                      : CachedImageWidget(imageUrl: imageList[0]),
                  Positioned(
                    bottom: 8,
                    left: 8,
                    child: Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: Colors.white,
                          radius: 10,
                          child: Text(
                            logoInitial,
                            style: const TextStyle(
                              color: Colors.redAccent,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          logoText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                            shadows: [
                              Shadow(
                                color: Colors.black45,
                                blurRadius: 4,
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );

    if (rotate) {
      // Apply rotation only to the outer card container
      return Transform.rotate(
        angle: rotateAngleDegree * (math.pi / 180),
        child: cardContent,
      );
    } else {
      return cardContent;
    }
  }

}

