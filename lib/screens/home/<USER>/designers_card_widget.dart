import 'dart:math' as math;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../../common/widgets/cached_image_network_widget.dart';

class DesignersCardWidget extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String logoText;
  final String logoInitial;
  final double width;
  final double height;
  final bool rotate;
  final double rotateAngleDegree; // in degrees, example: 270

  const DesignersCardWidget({
    super.key,
    required this.imageUrl,
    this.title = 'Designers',
    this.logoText = 'MuseLAB',
    this.logoInitial = 'M',
    this.width = 100,
    this.height = 200,
    this.rotate = false,
    this.rotateAngleDegree = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    Widget cardContent = Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: const Color(0xFFEDEDED), // Grey background - same as projects
        borderRadius: BorderRadius.circular(24), // Same as projects
        boxShadow: const [
          BoxShadow(
            color: Colors.black12, // Same as projects
            blurRadius: 6, // Same as projects
            offset: Offset(0, 3), // Same as projects
          ),
        ],
      ),
      child: Stack(
        children: [
          // Base layout with grey and white sections - VERTICAL layout
          Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Grey space (33% of height) - reduced slightly to give more white space
              Container(
                height: height * 0.33,
                decoration: const BoxDecoration(
                  color: Color(0xFFEDEDED),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(24),
                    topRight: Radius.circular(24),
                  ),
                ),
              ),
              // White space (67% of height) - increased by 2% for better balance
              Expanded(
                child: Container(
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                      bottomLeft: Radius.circular(24), // Match container curve
                      bottomRight: Radius.circular(24), // Match container curve
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Title positioned on white background - NO rotation for vertical
          Positioned(
            top: height * 0.34, // Position in white area - adjusted for reduced white space
            left: 0,
            right: 0,
            height: height * 0.12, // Reduced title area height to save space
            child: Center(
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 16, // Same as projects
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                  letterSpacing: 1.2, // Same as projects
                ),
              ),
            ),
          ),
          // Image positioned to touch white background edges - adjusted for increased white space
          Positioned(
            left: 0, // Touch left edge
            right: 0, // Touch right edge
            bottom: 0, // Touch bottom edge
            top: height * 0.45, // Start slightly lower to accommodate increased white space
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                  bottomLeft: Radius.circular(24), // Match white background curve
                  bottomRight: Radius.circular(24), // Match white background curve
                ),
                border: Border.all(
                  color: Colors.black.withValues(alpha: 0.4), // Black border - same as projects
                  width: 1.5, // Same as projects
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(18.5), // Account for border
                  topRight: Radius.circular(18.5),
                  bottomLeft: Radius.circular(22.5), // Account for border
                  bottomRight: Radius.circular(22.5),
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    CachedImageWidget(imageUrl: imageUrl),
                    // Logo positioned at bottom left - same as projects
                    Positioned(
                      bottom: 8,
                      left: 8,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Logo initial in circle - same as projects card
                          Container(
                            width: 20,
                            height: 20,
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              shape: BoxShape.circle,
                            ),
                            child: Center(
                              child: Text(
                                logoInitial,
                                style: const TextStyle(
                                  color: Colors.redAccent, // Same as projects
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 4),
                          // Logo text - same styling as projects card
                          Flexible(
                            child: Text(
                              logoText,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600, // Same as projects
                                fontSize: 10, // Same as projects
                                shadows: [
                                  Shadow(
                                    color: Colors.black54, // Same as projects
                                    blurRadius: 6, // Same as projects
                                    offset: Offset(1, 1), // Same as projects
                                  )
                                ],
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );

    if (rotate) {
      // Apply rotation only to the outer card container
      return Transform.rotate(
        angle: rotateAngleDegree * (math.pi / 180),
        child: cardContent,
      );
    } else {
      return cardContent;
    }
  }

}

