import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/home/<USER>/projects_card_widget.dart';

import '../../../common/mobilesize/sizeconfig.dart';
import '../../../common/widgets/responsive_wrapper.dart';
import '../../../constants/assets.dart';
import '../../../constants/strings.dart';
import '../../../services/responsive_service.dart';
import '../../../utils/utils.dart';

import '../controller/home_controller.dart';
import '../widgets/activity_card_widget.dart';
import '../widgets/buzzing_card_widget.dart';
import '../widgets/category_button_widget.dart';
import '../widgets/day_display_widget.dart';
import '../widgets/designers_card_widget.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Use Get.find to avoid recreating controller on every build
    final HomeController controller = Get.find<HomeController>();
    final ResponsiveService responsiveService = ResponsiveService.to;

    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: Colors.grey[300],
        body: Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: Utils.pngImageAsset(AssetStrings.homeBgImage),
              fit: BoxFit.cover,
            ),
          ),
          child: SafeArea(
            child: ResponsiveBuilder(
              builder: (context, deviceType) {
                return _buildHomeContent(controller, responsiveService, deviceType);
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHomeContent(HomeController controller, ResponsiveService responsiveService, DeviceType deviceType) {
    return Stack(
      children: [
        // Header
        Positioned(
          top: responsiveService.responsiveValue(mobile: 0.02 * height, tablet: 0.03 * height, desktop: 0.04 * height),
          left: responsiveService.responsiveValue(mobile: 15.0, tablet: 20.0, desktop: 30.0),
          child: Column(
            children: [
              Utils.pngImage(AssetStrings.homeLogoImage),
              SizedBox(height: responsiveService.responsiveValue(mobile: 15.0, tablet: 18.0, desktop: 20.0)),
              Obx(() => ResponsiveText(
                "${controller.earnedAmount}",
                baseFontSize: 12,
                color: Colors.blue,
                fontWeight: FontWeight.bold,
              )),
              Utils.pngImage(AssetStrings.homeArrowImage),
            ],
          ),
        ),

        // Day Display - moved down a little
        Positioned(
          top: responsiveService.responsiveValue(mobile: 0.32 * height, tablet: 0.27 * height, desktop: 0.22 * height),
          right: responsiveService.responsiveValue(mobile: 10.0, tablet: 20.0, desktop: 30.0),
          child: DayDisplayWidget(
            dayPrefix: controller.dayPrefix,
            daySuffix: controller.daySuffix,
          ),
        ),

        // Designers Card (top right) - increased height for better proportions
        Positioned(
          top: responsiveService.responsiveValue(mobile: 0.02 * height, tablet: 0.03 * height, desktop: 0.04 * height),
          right: responsiveService.responsiveValue(mobile: 20.0, tablet: 30.0, desktop: 40.0),
          child: InkWell(
            onTap: () => Get.toNamed('/designerListScreen', id: 2),
            child: DesignersCardWidget(
              imageUrl: 'https://unsplash.com/photos/38BThc6Q-ZI/download?force=true',
              title: "Designers",
              logoText: "DesignLab", // Specific designer logo xtext
              logoInitial: "D", // Specific designer logo initial
              height: responsiveService.responsiveValue(mobile: 0.28 * height, tablet: 0.20 * height, desktop: 0.24 * height), // Increased height for better space management
              width: responsiveService.responsiveValue(mobile: 0.33 * width, tablet: 0.25 * width, desktop: 0.20 * width), // Will be overridden by internal logic
            ),
          ),
        ),

        // Projects Card - moved down a little
        Positioned(
          top: responsiveService.responsiveValue(mobile: 0.32 * height, tablet: 0.27 * height, desktop: 0.22 * height),
          left: responsiveService.responsiveValue(mobile: 20.0, tablet: 30.0, desktop: 40.0),
          child: InkWell(
            onTap: () => Get.toNamed('/projectListScreen', id: 2),
            child: ProjectsCardWidget(
              imageUrl: 'https://unsplash.com/photos/38BThc6Q-ZI/download?force=true',
              title: "Projects",
              height: responsiveService.responsiveValue(mobile: 0.15 * height, tablet: 0.15 * height, desktop: 0.18 * height),
              cardWidth: responsiveService.responsiveValue(mobile: 0.2 * width, tablet: 0.25 * width, desktop: 0.20 * width),
            ),
          ),
        ),

        // Buzzing Card
        Positioned(
          bottom: responsiveService.responsiveValue(mobile: 0.1 * height, tablet: 0.12 * height, desktop: 0.15 * height),
          right: responsiveService.responsiveValue(mobile: 20.0, tablet: 30.0, desktop: 40.0),
          child: BuzzingCardWidget(
            imageList: controller.imageList,
            autoPlayCarousel: true,
            title: "Buzzing",
            useCarousel: true,
            height: responsiveService.responsiveValue(mobile: 0.28 * height, tablet: 0.25 * height, desktop: 0.22 * height),
            width: responsiveService.responsiveValue(mobile: 0.33 * width, tablet: 0.25 * width, desktop: 0.20 * width),
            logoInitials: controller.buzzingInitialsList,
            logoTexts: controller.buzzingLogoList,
          ),
        ),

        // Category Buttons
        Positioned(
          bottom: responsiveService.responsiveValue(mobile: 30.0, tablet: 40.0, desktop: 50.0),
          left: responsiveService.responsiveValue(mobile: 20.0, tablet: 30.0, desktop: 40.0),
          child: _buildCategoryButtons(responsiveService),
        ),
      ],
    );
  }

  Widget _buildCategoryButtons(ResponsiveService responsiveService) {
    final spacing = responsiveService.responsiveValue(mobile: 5.0, tablet: 8.0, desktop: 10.0);

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            const CategoryButton(
              label: Strings.elements,
              highlight: false,
            ),
            SizedBox(width: spacing),
            const CategoryButton(label: Strings.materials),
            SizedBox(width: spacing),
            const CategoryButton(label: Strings.colours),
          ],
        ),
        SizedBox(height: responsiveService.responsiveValue(mobile: 10.0, tablet: 12.0, desktop: 15.0)),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            const CategoryButton(label: Strings.resources),
            SizedBox(width: spacing),
            const CategoryButton(label: Strings.pros),
            SizedBox(width: spacing),
            const CategoryButton(
              label: Strings.tickle,
              highlight: true,
            ),
          ],
        ),
      ],
    );
  }
}
