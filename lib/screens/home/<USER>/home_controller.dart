import 'package:get/get.dart';
import 'package:intl/intl.dart';  // <-- You forgot to import this for DateFormat

class HomeController extends GetxController {
  var earnedAmount = 974000.obs;
  var imageList = <String>[].obs;
  var buzzingLogoList = <String>[].obs;
  var buzzingInitialsList = <String>[].obs;

  late String fullDay;
  late String dayPrefix;
  late String daySuffix;

  @override
  void onInit() {
    super.onInit();
    initializeDay();
    fetchImages(); // Call API here
  }

  void initializeDay() {
    fullDay = DateFormat('EEEE').format(DateTime.now());
    dayPrefix = fullDay.substring(0, 3);
    daySuffix = fullDay.length > 3 ? fullDay.substring(3) : '';
  }

  void fetchImages() {
    // Simulating API call with static data for now
    imageList.value = [
      'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800&q=80', // Modern dining room with table, chairs, and kitchen
      'https://images.unsplash.com/photo-1567538096630-e0c55bd6374c?w=800&q=80', // Cozy living room with natural lighting
      'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800&q=80', // Modern interior
    ];
    buzzingLogoList.value = [
      "MuseLab",
      "use",
      "MuseLab Buzz",
    ];
    buzzingInitialsList.value = [
      "M",
      "U",
      "MB",
    ];
  }
}
