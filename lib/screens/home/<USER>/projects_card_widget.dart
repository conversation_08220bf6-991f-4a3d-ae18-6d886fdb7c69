import 'package:flutter/material.dart';
import '../../../common/widgets/cached_image_network_widget.dart';

class ProjectsCardWidget extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String logoText;
  final String logoInitial;
  final double? cardWidth;
  final double? height;

  const ProjectsCardWidget({
    super.key,
    required this.imageUrl,
    this.title = 'Projects',
    this.logoText = 'MuseLAB',
    this.logoInitial = 'M',
    this.cardWidth,
    this.height = 160,
  });

  @override
  Widget build(BuildContext context) {
    // Get screen width
    final double screenWidth = MediaQuery.of(context).size.width;
    final double containerWidth = screenWidth * 0.6; // 70% of screen width
    final double containerHeight = height ?? 250.0; // Even broader height


    return Container(
      width: containerWidth,
      height: containerHeight,
      decoration: BoxDecoration(
        color: const Color(0xFFEDEDED), // Grey background
        borderRadius: BorderRadius.circular(24),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 6,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Base layout with grey and white sections
          Row(
            children: [
              // Grey space (30% of container)
              Expanded(
                flex: 30,
                child: Container(), // Empty grey space
              ),
              // Extended white space to cover all remaining area (70% of container)
              Expanded(
                flex: 55,
                child: Container(
                  height: containerHeight,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      bottomLeft: Radius.circular(20),
                      topRight: Radius.circular(24), // Match grey container curve
                      bottomRight: Radius.circular(24), // Match grey container curve
                    ),
                  ),
                ),
              ),
            ],
          ),
          // Title positioned on white background
          Positioned(
            left: containerWidth * 0.32, // Position in white area
            top: 0,
            bottom: 0,
            width: containerWidth * 0.18, // Title area width
            child: Center(
              child: RotatedBox(
                quarterTurns: -1, // Rotate 90 degrees counter-clockwise
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    letterSpacing: 1.2,
                  ),
                ),
              ),
            ),
          ),
          // Image positioned to touch white background edges - bigger size
          Positioned(
            right: 0, // Touch right edge
            top: 0,   // Touch top edge
            bottom: 0, // Touch bottom edge
            width: containerWidth * 0.55, // Image takes 55% of container width (bigger)
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  bottomLeft: Radius.circular(20),
                  topRight: Radius.circular(24), // Match white background curve
                  bottomRight: Radius.circular(24), // Match white background curve
                ),
                border: Border.all(
                  color: const Color(0x66000000), // Black border with 40% opacity
                  width: 1.5,
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(18.5),
                  bottomLeft: Radius.circular(18.5),
                  topRight: Radius.circular(22.5),
                  bottomRight: Radius.circular(22.5),
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Background image
                    CachedImageWidget(imageUrl: imageUrl),
                    // Logo overlay
                    Align(
                alignment: Alignment.bottomLeft,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Logo initial in circle
                      Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            logoInitial,
                            style: const TextStyle(
                              color: Colors.redAccent,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      // Logo text
                      Flexible(
                        child: Text(
                          logoText,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                            shadows: [
                              Shadow(
                                color: Colors.black54,
                                blurRadius: 6,
                                offset: Offset(1, 1),
                              )
                            ],
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
