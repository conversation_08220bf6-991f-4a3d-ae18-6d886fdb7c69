import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/projects/pages/project_list_sub_screen.dart';
import 'package:openslate/utils/routes.dart';

import '../../projects/controller/project_sub_list_controller.dart';

class ProfileCheckScreen extends StatelessWidget {
  // Dummy variable to simulate profile check
  // In a real app, you would check a user profile state from local storage or a service.
  RxBool hasProfile = false.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Profile Check'),
      ),
      body: Center(
        child: Obx(() {
          if (hasProfile.value) {
            // If profile exists, navigate to the profile screen or home screen
            return Text('Welcome to your profile!');
          } else {
            // If profile doesn't exist, show the message and buttons
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'You haven\'t created a profile yet.',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    // Navigate to Sign Up screen
                    Get.to(() => SignUpScreen());
                  },
                  child: Text('Sign Up'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
                SizedBox(height: 10),
                ElevatedButton(
                  onPressed: () {
                    Get.toNamed(Routes.signInScreen);
                    // Navigate to Sign In screen
                 //   Get.lazyPut(() => ProfileController());
                  //  Get.to(() => InstagramProfileScreen());
                  },
                  child: Text('Sign In'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    padding: EdgeInsets.symmetric(horizontal: 40, vertical: 15),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                ),
              ],
            );
          }
        }),
      ),
    );
  }
}

class SignUpScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Sign Up')),
      body: Center(child: Text('Sign Up Screen')),
    );
  }
}

class SignInScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Sign In')),
      body: Center(child: Text('Sign In Screen')),
    );
  }
}

