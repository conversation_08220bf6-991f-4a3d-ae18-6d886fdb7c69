import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProjectSummaryController extends GetxController {
  final List<File> selectedImages;
  final elementsPerImage = <List<SpaceElement>>[].obs;
  final hotspotPositions = <List<Offset>>[].obs;
  final lockedStates = <List<bool>>[].obs;
  final spaceNames = <String>[].obs;
  final isLoading = false.obs;
  final errorMessage = ''.obs;

  final currentImageIndex = 0.obs;
  final editingHotspotIndex = RxnInt();
  final activeElementIndex = RxnInt();
  final draggingHotspotIndex = RxnInt();
  final lastTapPosition = Rxn<Offset>();
  final textControllers = <int, Map<int, TextEditingController>>{}.obs;
  final focusNodes = <int, Map<int, FocusNode>>{}.obs;

  ProjectSummaryController(this.selectedImages) {
    _initializeDataStructures();
  }

  void _initializeDataStructures() {
    elementsPerImage.addAll(List.generate(selectedImages.length, (_) => []));
    hotspotPositions.addAll(List.generate(selectedImages.length, (_) => []));
    lockedStates.addAll(List.generate(selectedImages.length, (_) => []));
    spaceNames.addAll(List.generate(selectedImages.length, (_) => ''));
  }

  bool get isDragging => draggingHotspotIndex.value != null;

  List<SpaceElement> getElementsForImage(int imageIndex) {
    if (imageIndex < elementsPerImage.length) {
      return elementsPerImage[imageIndex];
    }
    return [];
  }

  TextEditingController getController(int imageIndex, int index, String initialText) {
    textControllers.putIfAbsent(imageIndex, () => {});
    if (!textControllers[imageIndex]!.containsKey(index)) {
      textControllers[imageIndex]![index] = TextEditingController(text: initialText);
    }
    return textControllers[imageIndex]![index]!;
  }

  FocusNode getFocusNode(int imageIndex, int index) {
    focusNodes.putIfAbsent(imageIndex, () => {});
    focusNodes[imageIndex]!.putIfAbsent(index, () => FocusNode());
    return focusNodes[imageIndex]![index]!;
  }

  String getSpaceNameForImage(int imageIndex) {
    return imageIndex < spaceNames.length ? spaceNames[imageIndex] : '';
  }

  String getCurrentSpaceName() => getSpaceNameForImage(currentImageIndex.value);

  bool isSpaceNameSaved(int index) => index < spaceNames.length && spaceNames[index].trim().isNotEmpty;

  void setCurrentImageIndex(int index) {
    currentImageIndex.value = index;
    activeElementIndex.value = null;
    draggingHotspotIndex.value = null;
  }

  void setActiveElementIndex(int? index) => activeElementIndex.value = index;
  void setDraggingState(int? index) => draggingHotspotIndex.value = index;
  void setLastTapPosition(Offset position) => lastTapPosition.value = position;

  void saveSpaceName(int imageIndex, String name) {
    if (name.isEmpty) return;
    if (imageIndex < spaceNames.length) {
      spaceNames[imageIndex] = name;
    } else {
      spaceNames.add(name);
    }
  }

  void setEditingHotspotIndex(int? index) => editingHotspotIndex.value = index;

  bool isHotspotDisabled(int imageIndex, int hotspotIndex) =>
      editingHotspotIndex.value != null && editingHotspotIndex.value != hotspotIndex;

  void addHotspot(int imageIndex, Offset position) {
    if (!isSpaceNameSaved(imageIndex)) return;
    if (elementsPerImage[imageIndex].length >= 7) return; // Changed from 5 to 7
    if (_isTooCloseToOtherHotspots(imageIndex, position)) return;

    while (hotspotPositions.length <= imageIndex) hotspotPositions.add([]);
    while (lockedStates.length <= imageIndex) lockedStates.add([]);
    while (elementsPerImage.length <= imageIndex) elementsPerImage.add([]);

    hotspotPositions[imageIndex].add(position);
    lockedStates[imageIndex].add(false);
    elementsPerImage[imageIndex].add(SpaceElement());
  }

  void addHotspotAtCenter(int imageIndex, GlobalKey stackKey) {
    if (elementsPerImage[imageIndex].length >= 7) return; // Changed from 5 to 7

    final box = stackKey.currentContext?.findRenderObject() as RenderBox?;
    if (box != null) {
      final center = Offset(box.size.width / 2, box.size.height / 2);
      addHotspot(imageIndex, center);

      // Activate the new hotspot
      final newIndex = elementsPerImage[imageIndex].length - 1;
      setActiveElementIndex(newIndex);
      setEditingHotspotIndex(newIndex);
    }
  }

  bool _isTooCloseToOtherHotspots(int imageIndex, Offset newPosition) {
    if (imageIndex >= hotspotPositions.length) return false;
    const minDistance = 100.0;
    return hotspotPositions[imageIndex].any((position) =>
    (position - newPosition).distance < minDistance);
  }

  void updateHotspotPosition(int imageIndex, int hotspotIndex, Offset newPosition) {
    if (imageIndex < hotspotPositions.length && hotspotIndex < hotspotPositions[imageIndex].length) {
      hotspotPositions[imageIndex][hotspotIndex] = newPosition;
    }
  }

  void updateElement(int imageIndex, int elementIndex, SpaceElement element) {
    if (imageIndex < elementsPerImage.length && elementIndex < elementsPerImage[imageIndex].length) {
      elementsPerImage[imageIndex][elementIndex] = element;
    }
  }

  void deleteHotspot(int imageIndex, int hotspotIndex) {
    if (imageIndex < elementsPerImage.length && hotspotIndex < elementsPerImage[imageIndex].length) {
      elementsPerImage[imageIndex].removeAt(hotspotIndex);
      hotspotPositions[imageIndex].removeAt(hotspotIndex);
      lockedStates[imageIndex].removeAt(hotspotIndex);

      if (activeElementIndex.value == hotspotIndex) {
        activeElementIndex.value = null;
      } else if (activeElementIndex.value != null && activeElementIndex.value! > hotspotIndex) {
        activeElementIndex.value = activeElementIndex.value! - 1;
      }
    }
  }

  void toggleLock(int imageIndex, int hotspotIndex) {
    if (imageIndex < lockedStates.length && hotspotIndex < lockedStates[imageIndex].length) {
      lockedStates[imageIndex][hotspotIndex] = !lockedStates[imageIndex][hotspotIndex];
    }
  }

  bool isDraggingOnImage(int imageIndex) => currentImageIndex.value == imageIndex && isDragging;

  bool validateForm() {
    for (int i = 0; i < spaceNames.length; i++) {
      if (spaceNames[i].trim().isEmpty) return false;
      for (var element in elementsPerImage[i]) {
        if (element.name.trim().isEmpty) return false;
      }
    }
    return true;
  }

  Future<bool> saveProject() async {
    if (!validateForm()) return false;
    try {
      isLoading(true);
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      return true;
    } catch (e) {
      errorMessage.value = 'Failed to save project';
      return false;
    } finally {
      isLoading(false);
    }
  }
}

class SpaceElement {
  final String name;
  final String brand;
  final String collection;
  final String material;
  final String price;
  final String description;
  final Map<String, dynamic> additionalFields;

  SpaceElement({
    this.name = '',
    this.brand = '',
    this.collection = '',
    this.material = '',
    this.price = '',
    this.description = '',
    this.additionalFields = const {},
  });

  SpaceElement copyWith({
    String? name,
    String? brand,
    String? collection,
    String? material,
    String? price,
    String? description,
    Map<String, dynamic>? additionalFields,
  }) {
    return SpaceElement(
      name: name ?? this.name,
      brand: brand ?? this.brand,
      collection: collection ?? this.collection,
      material: material ?? this.material,
      price: price ?? this.price,
      description: description ?? this.description,
      additionalFields: additionalFields ?? this.additionalFields,
    );
  }

  Map<String, dynamic> toJson() => {
    'name': name,
    'brand': brand,
    'collection': collection,
    'material': material,
    'price': price,
    'description': description,
    ...additionalFields,
  };
}