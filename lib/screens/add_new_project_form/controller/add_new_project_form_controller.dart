import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:openslate/screens/add_new_project_form/pages/project_summary_screen.dart';

class ProjectFormController extends GetxController {
  // Form controllers with null checks
  final TextEditingController projectName = TextEditingController();
  final TextEditingController location = TextEditingController();
  final TextEditingController year = TextEditingController();
  final TextEditingController duration = TextEditingController();
  final TextEditingController area = TextEditingController();
  final TextEditingController cost = TextEditingController();
  final TextEditingController about = TextEditingController();

  // State variables with default values
  final RxInt currentStep = 1.obs;
  final RxBool isLoading = false.obs;
  final RxBool showProfessionalSelection = false.obs;
  final RxBool isAddingProfessionals = true.obs;
  final RxInt selectedYear = DateTime.now().year.obs;
  final RxString selectedCategory = ''.obs;

  // Data lists with maximum limits
  final RxList<String> selectedProfessionals = <String>[].obs;
  final RxList<File> selectedImages = <File>[].obs;
  final RxList<ImageSpace> imageSpaces = <ImageSpace>[].obs;
  final RxInt currentImageIndex = 0.obs;

  // Constants
  static const int maxImageCount = 8;
  static const int maxImageSizeMB = 12;

  final List<String> categories = [
    'Restaurant', 'Cafe', 'Home', 'Hotel', 'Clubhouse',
    'Institutional', 'Healthcare', 'Office', 'Retail',
    'Public Space', 'Sales Office', 'Theatre', 'Industry', 'Lobby'
  ];

  final List<String> professionalOptions = [
    'Principle Designer', 'Designer', 'PMC', 'Master Contractor',
    'Civil Contractor', 'Electrical Contractor', 'Plumbing Contractor',
    'Carpentry', 'Painting', 'Polishing', 'False Ceiling',
  ];

  // Navigation methods with validation
  void nextStep() {
    if (currentStep.value == 1 && !_validateStep1()) return;
    if (currentStep.value == 2 && !_validateStep2()) return;

    if (showProfessionalSelection.value) {
      showProfessionalSelection.value = false;
    } else if (currentStep.value < 3) {
      currentStep.value += 1;
    } else {
      submitProject();
    }
  }

  bool _validateStep1() {
    if (projectName.text.isEmpty) {
      Get.snackbar('Error', 'Please enter project name');
      return false;
    }
    if (selectedCategory.value.isEmpty) {
      Get.snackbar('Error', 'Please select a category');
      return false;
    }
    return true;
  }

  bool _validateStep2() {
    if (isAddingProfessionals.value && selectedProfessionals.isEmpty) {
      Get.snackbar('Error', 'Please add at least one professional');
      return false;
    }
    return true;
  }

  // Improved image handling with error management
  Future<void> pickImages() async {
    try {
      if (selectedImages.length >= maxImageCount) {
        Get.snackbar('Limit Reached', 'You can only add $maxImageCount images');
        return;
      }

      final ImagePicker picker = ImagePicker();
      final List<XFile>? pickedFiles = await picker.pickMultiImage(
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 85,
      );

      if (pickedFiles != null) {
        for (var file in pickedFiles) {
          if (selectedImages.length >= maxImageCount) break;

          final imageFile = File(file.path);
          final sizeInMB = await imageFile.length() / (1024 * 1024);

          if (sizeInMB > maxImageSizeMB) {
            Get.snackbar('Error', '${file.name} exceeds $maxImageSizeMB MB limit');
            continue;
          }

          selectedImages.add(imageFile);
        }

        // ✅ Reinitialize imageSpaces
        initializeImageSpaces();
      }
    } catch (e) {
      Get.snackbar('Error', 'Failed to pick images: ${e.toString()}');
    }
  }


  void removeImage(int index) {
    if (index >= 0 && index < selectedImages.length) {
      selectedImages.removeAt(index);
      if (imageSpaces.length > index) {
        imageSpaces.removeAt(index);
      }
    }
  }

  void initializeImageSpaces() {
    imageSpaces.value = selectedImages
        .where((file) => !imageSpaces.any((space) => space.file.path == file.path))
        .map((file) => ImageSpace(file: file)) // ✅ Now supports `hotspots`
        .toList();
  }


  // Safe list operations
  void saveSpaceName(int index, String name, String type) {
    if (index >= 0 && index < imageSpaces.length) {
      imageSpaces[index] = imageSpaces[index].copyWith(
        name: name,
        type: type,
        isNamed: true,
      );
    }
  }

  // Project submission with better state management
  Future<void> submitProject() async {
    if (selectedImages.isEmpty) {
      Get.snackbar('Error', 'Please add at least one image');
      return;
    }
      isLoading.value = true;
    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      Get.off(() => ProjectSummaryScreen(
        selectedImages: List<File>.from(selectedImages),
      ));
    } catch (e) {
      Get.snackbar('Error', 'Failed to submit project: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  void clearTempState() {
    selectedImages.clear();
    imageSpaces.clear();
    selectedProfessionals.clear();
    currentImageIndex.value = 0;
    // Clear form fields if needed
    projectName.clear();
    location.clear();
    year.clear();
    duration.clear();
    area.clear();
    cost.clear();
    about.clear();
  }

  @override
  void onClose() {
    projectName.dispose();
    location.dispose();
    year.dispose();
    duration.dispose();
    area.dispose();
    cost.dispose();
    about.dispose();
    super.onClose();
  }
}
class ImageSpace {
  final File file;
  final String name;
  final String type;
  final bool isNamed;
  final List<SpaceElement> elements;

  // ✅ NEW LINE: reactive list for storing hotspot metadata
  final RxList<Map<String, dynamic>> hotspots;

  ImageSpace({
    required this.file,
    this.name = '',
    this.type = '',
    this.isNamed = false,
    this.elements = const [],

    // ✅ NEW LINE: initialize the hotspots list
    RxList<Map<String, dynamic>>? hotspots,
  }) : hotspots = hotspots ?? <Map<String, dynamic>>[].obs;

  ImageSpace copyWith({
    File? file,
    String? name,
    String? type,
    bool? isNamed,
    List<SpaceElement>? elements,

    // ✅ NEW LINE
    RxList<Map<String, dynamic>>? hotspots,
  }) {
    return ImageSpace(
      file: file ?? this.file,
      name: name ?? this.name,
      type: type ?? this.type,
      isNamed: isNamed ?? this.isNamed,
      elements: elements ?? this.elements,

      // ✅ NEW LINE
      hotspots: hotspots ?? this.hotspots,
    );
  }
}



class SpaceElement {
  final String name;
  final String brand;
  final String collection;
  final String material;
  final String price;
  final String description;

  SpaceElement({
    required this.name,
    required this.brand,
    required this.collection,
    required this.material,
    required this.price,
    required this.description,
  });
}