import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:openslate/utils/utils.dart';
import '../controller/project_summary_controller.dart';

class ProjectSummaryScreen extends StatefulWidget {
  final List<File> selectedImages;

  const ProjectSummaryScreen({Key? key, required this.selectedImages}) : super(key: key);

  @override
  State<ProjectSummaryScreen> createState() => _ProjectSummaryScreenState();
}

class _ProjectSummaryScreenState extends State<ProjectSummaryScreen> with TickerProviderStateMixin {
  late final ProjectSummaryController controller;
  final List<GlobalKey> _stackKeys = [];
  final FocusNode _spaceNameFocusNode = FocusNode();
  final _pageController = PageController();
  final _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    controller = Get.put(ProjectSummaryController(widget.selectedImages));
    for (int i = 0; i < widget.selectedImages.length; i++) {
      _stackKeys.add(GlobalKey());
    }
  }

  @override
  void dispose() {
    _spaceNameFocusNode.dispose();
    _pageController.dispose();
    _scrollController.dispose();
    Get.delete<ProjectSummaryController>();
    super.dispose();
  }

  void _handleNewHotspot(int imageIndex) {
    final position = controller.lastTapPosition.value;
    if (position == null) return;
    controller.addHotspot(imageIndex, position);
    controller.lastTapPosition.value = null;

    final newIndex = controller.getElementsForImage(imageIndex).length - 1;
    controller.setActiveElementIndex(newIndex);
    controller.setEditingHotspotIndex(newIndex);
  }

  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: KeyboardVisibilityBuilder(
        builder: (context, isKeyboardVisible) {
          return SafeArea(
            child: Stack(
              children: [
                Column(
                  children: [
                    // Custom AppBar Row
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(Icons.arrow_back, color: Colors.black),
                            onPressed: () => Navigator.pop(context),
                          ),
                          const SizedBox(width: 8),
                          Obx(() => Text(
                            controller.getCurrentSpaceName().isNotEmpty
                                ? controller.getCurrentSpaceName()
                                : "Add Elements",
                            style: const TextStyle(fontSize: 14, color: Colors.black),
                          )),
                        ],
                      ),
                    ),
                    Expanded(
                      child: _buildPageView(),
                    ),  

                    // Spacer for BottomNavigation if keyboard not visible
                    if (!isKeyboardVisible &&
                        controller.spaceNames.any((name) => name.isNotEmpty))
                      const SizedBox(height: 60),
                  ],
                ),

                // Bottom Navigation Bar
                if (!isKeyboardVisible &&
                    controller.spaceNames.any((name) => name.isNotEmpty))
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: _buildBottomNavigation(),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }


  Widget _buildPageView() {
    return PageView.builder(
      controller: _pageController,
      physics: controller.isDragging
          ? const NeverScrollableScrollPhysics()
          : const PageScrollPhysics(),
      itemCount: widget.selectedImages.length,
      onPageChanged: (index) => controller.setCurrentImageIndex(index),
      itemBuilder: (context, index) {
        return Obx(() {
          final hasSpaceName = controller.getSpaceNameForImage(index).isNotEmpty;
          final elements = controller.getElementsForImage(index);
          return SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              children: [
                _buildImageWithHotspots(index),
                if (hasSpaceName) _buildTabBar(index),
                if (hasSpaceName) _buildTabBarView(index),
              ],
            ),
          );
        });
      },
    );
  }

  Widget _buildImageWithHotspots(int imageIndex) {
    return Obx(() {
      final hasSpaceName = controller.getSpaceNameForImage(imageIndex).isNotEmpty;
      return GestureDetector(
        onTapDown: (details) => controller.lastTapPosition.value = details.localPosition,
        onDoubleTap: () {
          if (hasSpaceName && controller.lastTapPosition.value != null) {
            if (controller.getElementsForImage(imageIndex).length >= 7) { // Changed from 5 to 7
              ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('You can only add 7 hotspots per image')));
              return;
            }
            _handleNewHotspot(imageIndex);
          }
        },
        child: Stack(
          key: _stackKeys[imageIndex],
          children: [
            SizedBox(
              height: MediaQuery.of(context).size.height * 0.5,
              width: double.infinity,
              child: Image.file(
                widget.selectedImages[imageIndex],
                fit: BoxFit.cover,
                cacheWidth: (MediaQuery.of(context).size.width * 2).toInt(),
              ),
            ),
            if (hasSpaceName) ..._buildHotspots(imageIndex),
            if (!hasSpaceName) _buildSpaceNameInput(imageIndex),
          ],
        ),
      );
    });
  }

  Widget _buildSpaceNameInput(int imageIndex) {
    final textController = TextEditingController(
        text: controller.getSpaceNameForImage(imageIndex));

    return Positioned(
      left: 0,
      right: 0,
      top: MediaQuery.of(context).size.height * 0.16,
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.75,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.white70,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              const Text("Name of Space", style: TextStyle(color: Colors.black, fontSize: 14)),
              const SizedBox(height: 4),
              TextField(
                controller: textController,
                focusNode: _spaceNameFocusNode,
                style: const TextStyle(fontSize: 14),
                decoration: InputDecoration(
                  hintText: "Type name..",
                  hintStyle: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                  isDense: true,
                  contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey.shade200),
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              Align(
                alignment: Alignment.bottomRight,
                child: GestureDetector(
                  onTap: () {
                    if (textController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Please enter space name')));
                      return;
                    }
                    controller.saveSpaceName(imageIndex, textController.text.trim());
                    controller.addHotspotAtCenter(imageIndex, _stackKeys[imageIndex]);
                    _spaceNameFocusNode.unfocus();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 5),
                    decoration: BoxDecoration(
                      color:  HexColor("383838"),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Text("Save",
                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500, fontSize: 13)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildHotspots(int imageIndex) {
    return controller.hotspotPositions[imageIndex].asMap().entries.map((entry) {
      final index = entry.key;
      final pos = entry.value;
      final isLocked = controller.lockedStates[imageIndex][index];
      final element = controller.elementsPerImage[imageIndex][index];
      final isDisabled = controller.isDragging && controller.draggingHotspotIndex?.value != index ||
          controller.isHotspotDisabled(imageIndex, index);

      return KeyedSubtree(
        key: ValueKey('hotspot_${imageIndex}_$index'),
        child: Positioned(
          left: pos.dx.clamp(20.0, MediaQuery.of(context).size.width - 140),
          top: pos.dy.clamp(20.0, MediaQuery.of(context).size.height * 0.5 - 80),
          child: Material(
            type: MaterialType.transparency,
            child: isLocked || isDisabled
                ? Opacity(
              opacity: isDisabled ? 0.5 : 1.0,
              child: _buildHotspotContent(isLocked, element, imageIndex, index),
            )
                : Draggable(
              feedback: Material(
                color: Colors.transparent,
                child: _buildHotspotContent(isLocked, element, imageIndex, index),
              ),
              childWhenDragging: Container(),
              child: _buildHotspotContent(isLocked, element, imageIndex, index),
              onDragStarted: () {
                controller.setDraggingState(index);
                _pageController.jumpToPage(_pageController.page!.toInt());
              },
              onDragEnd: (details) {
                final box = _stackKeys[imageIndex].currentContext?.findRenderObject() as RenderBox?;
                if (box != null) {
                  var localOffset = box.globalToLocal(details.offset);
                  localOffset = Offset(
                    localOffset.dx.clamp(20.0, box.size.width - 120),
                    localOffset.dy.clamp(20.0, box.size.height - 80),
                  );
                  controller.updateHotspotPosition(imageIndex, index, localOffset);
                  controller.setDraggingState(null);
                }
              },
              onDragUpdate: (details) {
                _pageController.jumpToPage(_pageController.page!.toInt());
              },
            ),
          ),
        ),
      );
    }).toList();
  }

  Widget _buildHotspotContent(bool isLocked, SpaceElement element, int imageIndex, int index) {
    return Obx(() {
      final isActive = controller.editingHotspotIndex?.value == index;
      final isFirstHotspot = index == 0;
      final circleColor = isLocked
          ? Colors.red
          : (isActive || isFirstHotspot)
          ? Colors.white
          : Colors.white.withOpacity(0.5);

      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 🟢 Perfectly centered circle
          GestureDetector(
            onTap: () {
              controller.setActiveElementIndex(index);
              controller.setEditingHotspotIndex(index);
            },
            child: Container(
              width: 12,
              height: 12,
              margin: const EdgeInsets.only(bottom: 4),
              decoration: BoxDecoration(
                color: circleColor,
                shape: BoxShape.circle,
              ),
            ),
          ),

          Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Pill TextField
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8),
                  borderRadius: BorderRadius.circular(30),
                ),
                constraints: const BoxConstraints(minWidth: 60),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 80),
                  child: TextField(
                    controller: controller.getController(imageIndex, index, element.name),
                    focusNode: controller.getFocusNode(imageIndex, index),
                    onTap: () {
                      controller.setActiveElementIndex(index);
                      controller.setEditingHotspotIndex(index);
                    },
                    onChanged: (value) {
                      controller.updateElement(
                        imageIndex,
                        index,
                        element.copyWith(name: value),
                      );
                    },
                    decoration: const InputDecoration(
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 4, horizontal: 0),
                      border: InputBorder.none,
                      hintText: 'Element',
                      hintStyle: TextStyle(color: Colors.grey, fontSize: 10),
                    ),
                    style: const TextStyle(fontSize: 10, color: Colors.black),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              // Tight Three-dot menu
              Container(
                height: 24,
                alignment: Alignment.center,
                margin: const EdgeInsets.only(left: 2), // Tiny gap
                child: PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, size: 14, color: Colors.white),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(minWidth: 0),
                  onSelected: (value) {
                    if (value == 'delete') {
                      controller.deleteHotspot(imageIndex, index);
                    } else if (value == 'lock') {
                      controller.toggleLock(imageIndex, index);
                    }
                  },
                  itemBuilder: (BuildContext context) => [
                    PopupMenuItem(
                      value: 'lock',
                      child: Text(isLocked ? 'Unlock' : 'Lock'),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Text('Delete', style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      );
    });
  }


/*
  Widget _buildHotspotContent(bool isLocked, SpaceElement element, int imageIndex, int index) {
    return Obx(() {
      final isActive = controller.editingHotspotIndex?.value == index;
      final isFirstHotspot = index == 0;
      final circleColor = isLocked
          ? Colors.red
          : (isActive || isFirstHotspot)
          ? Colors.white
          : Colors.white.withOpacity(0.5);

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Circle indicator
          GestureDetector(
            onTap: () {
              controller.setActiveElementIndex(index);
              controller.setEditingHotspotIndex(index);

              Future.delayed(const Duration(milliseconds: 50), () {
                controller.getFocusNode(imageIndex, index).requestFocus();
              });
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 5),
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: circleColor,
                shape: BoxShape.circle,
              ),
            ),
          ),
          const SizedBox(height: 4),

          // Show the textfield only if active
          if (isActive)
            Container(
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 80,
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(100),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: TextField(
                      controller: controller.getController(imageIndex, index, element.name),
                      focusNode: controller.getFocusNode(imageIndex, index),
                      onChanged: (value) {
                        controller.updateElement(
                          imageIndex,
                          index,
                          element.copyWith(name: value),
                        );
                      },
                      decoration: const InputDecoration(
                        isDense: true,
                        contentPadding: EdgeInsets.symmetric(vertical: 6),
                        border: InputBorder.none,
                        hintText: 'Name',
                        hintStyle: TextStyle(color: Colors.grey, fontSize: 10),
                      ),
                      style: const TextStyle(fontSize: 10),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(width: 8),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, size: 14, color: Colors.white),
                    onSelected: (value) {
                      if (value == 'delete') {
                        controller.deleteHotspot(imageIndex, index);
                      } else if (value == 'lock') {
                        controller.toggleLock(imageIndex, index);
                      }
                    },
                    itemBuilder: (BuildContext context) => [
                      PopupMenuItem(
                        value: 'lock',
                        child: Text(isLocked ? 'Unlock' : 'Lock'),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Text('Delete', style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      );
    });
  }
 */


  Widget _buildTabBar(int index) {
    final elements = controller.getElementsForImage(index);
    if (elements.isEmpty) return Container();

    return Obx(() {
      final tabController = TabController(
        length: elements.length,
        vsync: this,
        initialIndex: (controller.activeElementIndex?.value ?? 0).clamp(0, elements.length - 1),
      );

      tabController.addListener(() {
        if (!tabController.indexIsChanging) {
          controller.setActiveElementIndex(tabController.index);
          controller.setEditingHotspotIndex(tabController.index);
        }
      });

      return Container(
          color: Colors.grey.shade200,
          height: 45,
          child: TabBar(
          controller: tabController,
          isScrollable: true,
          labelColor: Colors.black,

          unselectedLabelColor: Colors.grey.shade600,
          indicator: const BoxDecoration(
          border: Border(bottom: BorderSide(color: Colors.black, width: 2))),
           padding: EdgeInsets.zero, // Remove default padding
           labelPadding: EdgeInsets.zero, // Remove default label padding
            tabs: elements.asMap().entries.map((entry) {
              final idx = entry.key;
              final element = entry.value;
              final isActive = controller.editingHotspotIndex?.value == idx;

              return Tab(
                child: Container(
                  margin: const EdgeInsets.only(right: 6),
                 // padding: const EdgeInsets.symmetric(horizontal: 4,vertical: 2),
                   padding:  EdgeInsets.only(left: 15,top: 2,bottom: 2),

                  decoration: BoxDecoration(
                    color: isActive ? Colors.white : Colors.white.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Flexible(
                        child: Text(
                          element.name.isNotEmpty ? element.name : 'Element ${idx + 1}',
                          style: const TextStyle(
                            fontSize: 12, // slightly smaller font
                            fontWeight: FontWeight.w400,
                            color: Colors.black87,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 6),
                      GestureDetector(
                        onTap: () {
                          if (elements.length < 7) {
                            controller.addHotspotAtCenter(index, _stackKeys[index]);
                          } else {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('Maximum 7 hotspots allowed')),
                            );
                          }
                        },
                        child: Container(
                          width: 24, // larger size
                          height: 24,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.black,),
                          ),
                          child: const Center(
                            child: Icon(Icons.add, size: 13, color: Colors.black), // bigger icon
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),


          ),
      );
    });
  }

  Widget _buildTabBarView(int index) {
    final elements = controller.getElementsForImage(index);
    if (elements.isEmpty) return Container();

    return Obx(() {
      final tabController = TabController(
        length: elements.length,
        vsync: this,
        initialIndex: (controller.activeElementIndex?.value ?? 0).clamp(0, elements.length - 1),
      );

      return AbsorbPointer(
        absorbing: controller.isDragging,
        child: SizedBox(
          height: 300,
          child: TabBarView(
            controller: tabController,
            physics: controller.isDragging
                ? const NeverScrollableScrollPhysics()
                : null,
            children: elements.map((element) {
              final idx = elements.indexOf(element);
              return ElementForm(
                element: element,
                onChanged: (updatedElement) {
                  controller.updateElement(index, idx, updatedElement);
                },
                onEditingStarted: () {
                  controller.setEditingHotspotIndex(idx);
                },
                isEditing: controller.editingHotspotIndex?.value == idx,
              );
            }).toList(),
          ),
        ),
      );
    });
  }

  Widget _buildBottomNavigation() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: HexColor("EBEBEB"),
        border: Border(top: BorderSide(color: Colors.grey.shade200)),

      ),

      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => Navigator.pop(context),
              child: const Text(
                "Back",
                style: TextStyle(fontSize: 12),
                textAlign: TextAlign.left,
              ),
            ),
          ),
          const
          SizedBox(width: 10),
          Expanded(
            child: Obx(() => ElevatedButton(
              onPressed: controller.isLoading.value ? null : _handleSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: HexColor("383838"),
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: controller.isLoading.value
                  ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: Colors.white),
              )
                  : const Text(
                "Save and Next",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500),
              ),
            )),
          ),
        ],
      ),
    );
  }

  void _handleSave() {
    if (controller.validateForm()) {
      controller.saveProject().then((success) {
        if (success) {
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Project saved successfully')));
        }
      }).catchError((error) {
        ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Failed to save project: $error')));
      });
    }
  }
}


class ElementForm extends StatefulWidget {
  final SpaceElement element;
  final ValueChanged<SpaceElement> onChanged;
  final VoidCallback onEditingStarted;
  final bool isEditing;

  const ElementForm({
    Key? key,
    required this.element,
    required this.onChanged,
    required this.onEditingStarted,
    required this.isEditing,
  }) : super(key: key);

  @override
  State<ElementForm> createState() => _ElementFormState();
}

class _ElementFormState extends State<ElementForm> {
  late TextEditingController _nameController;
  late TextEditingController _brandController;
  late TextEditingController _collectionController;
  late TextEditingController _priceController;
  late TextEditingController _descriptionController;
  String? _selectedMaterial;

  final List<String> _materialOptions = [
    'Wood', 'Metal', 'Plastic', 'Glass', 'Fabric', 'Leather', 'Stone'
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.element.name);
    _brandController = TextEditingController(text: widget.element.brand);
    _collectionController = TextEditingController(text: widget.element.collection);
    _priceController = TextEditingController(text: widget.element.price);
    _descriptionController = TextEditingController(text: widget.element.description);
    _selectedMaterial = widget.element.material.isNotEmpty ? widget.element.material : null;

    _nameController.addListener(_updateElement);
    _brandController.addListener(_updateElement);
    _collectionController.addListener(_updateElement);
    _priceController.addListener(_updateElement);
    _descriptionController.addListener(_updateElement);
  }

  void _updateElement() {
    widget.onChanged(widget.element.copyWith(
      name: _nameController.text,
      brand: _brandController.text,
      collection: _collectionController.text,
      material: _selectedMaterial ?? '',
      price: _priceController.text,
      description: _descriptionController.text,
    ));
  }

  Widget _buildLabelRow(String label, Widget inputWidget) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              child: Text(
                label,
                style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w400),
              ),
            ),
            const Text(":", style: TextStyle(fontSize: 13)),
            const SizedBox(width: 5),
            Expanded(child: inputWidget),
          ],
        ),
        const Divider(height: 16, thickness: 0.8, color: Colors.grey),
      ],
    );
  }


  Widget _buildMaterialDropdown() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          const SizedBox(
            width: 80,
            child: Text("Material:", style: TextStyle(fontSize: 13)),
          ),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedMaterial,
              items: _materialOptions
                  .map((material) => DropdownMenuItem<String>(
                value: material,
                child: Text(material),
              ))
                  .toList(),
              onChanged: widget.isEditing
                  ? (value) {
                setState(() => _selectedMaterial = value);
                _updateElement();
              }
                  : null,
              decoration: const InputDecoration(
                isDense: true,
                border: UnderlineInputBorder(),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.black),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 6),
              ),
              style: const TextStyle(fontSize: 13),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Element ${widget.element.name}",
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),

                    _buildLabelRow(
                      "Brand",
                      TextField(
                        controller: _brandController,
                        onTap: widget.onEditingStarted,
                        enabled: widget.isEditing,
                        decoration: const InputDecoration(
                          isDense: true,
                          hintText: "Search",
                          hintStyle: TextStyle(fontSize: 13),
                          border: InputBorder.none,
                        ),
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),

                    _buildLabelRow(
                      "Collection",
                      TextField(
                        controller: _collectionController,
                        onTap: widget.onEditingStarted,
                        enabled: widget.isEditing,
                        decoration: const InputDecoration(
                          isDense: true,
                          border: InputBorder.none,
                        ),
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),

                    _buildLabelRow(
                      "Material",
                      DropdownButtonFormField<String>(
                        value: _selectedMaterial,
                        items: _materialOptions
                            .map((material) => DropdownMenuItem<String>(
                          value: material,
                          child: Text(material),
                        ))
                            .toList(),
                        onChanged: widget.isEditing
                            ? (value) {
                          setState(() => _selectedMaterial = value);
                          _updateElement();
                        }
                            : null,
                        decoration: const InputDecoration(
                          isDense: true,
                          hintText: "Search for Material",
                          border: InputBorder.none,
                        ),
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),

                    _buildLabelRow(
                      "Price",
                      TextField(
                        controller: _priceController,
                        onTap: widget.onEditingStarted,
                      //  enabled: widget.isEditing,
                        decoration: const InputDecoration(
                          isDense: true,
                          border: InputBorder.none,
                        ),
                        style: const TextStyle(fontSize: 13),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(width: 16),
              Expanded(
                flex: 1,
                child: Table(
                  border: TableBorder.all(color: Colors.grey.shade300),
                  columnWidths: const {
                    0: FlexColumnWidth(),
                    1: FlexColumnWidth(),
                  },
                  children: [
                    TableRow(
                      decoration: BoxDecoration(color: Colors.grey.shade100),
                      children: const [
                        Padding(
                          padding: EdgeInsets.all(6.0),
                          child: Text("Points available",
                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 8)),
                        ),
                        Padding(
                          padding: EdgeInsets.all(6.0),
                          child: Text("Points earned",
                              style: TextStyle(color: Colors.blue, fontWeight: FontWeight.bold, fontSize: 8)),
                        ),
                      ],
                    ),
                    for (int i = 0; i < 5; i++)
                      TableRow(children: [
                        const Padding(padding: EdgeInsets.all(8.0), child: Text("")),
                        const Padding(padding: EdgeInsets.all(8.0), child: Text("")),
                      ]),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          const Align(
            alignment: Alignment.centerLeft,
            child: Text("Additional Comments",
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w400)),
          ),
          const SizedBox(height: 6),
          TextField(
            controller: _descriptionController,
            onTap: widget.onEditingStarted,
            enabled: widget.isEditing,
            maxLines: 5,
            decoration: InputDecoration(
              filled: true,
              fillColor: Colors.grey.shade100,
              hintText: "Enter comments here...",
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide.none),
            ),
            style: const TextStyle(fontSize: 13),
          ),
        ],
      ),
    );
  }
}