import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/add_new_project_form/controller/add_new_project_form_controller.dart';
import 'package:openslate/utils/utils.dart';

import '../widgets/custom_textfield_widget.dart';

class ProjectFormScreen extends StatefulWidget {
  const ProjectFormScreen({super.key});

  @override
  State<ProjectFormScreen> createState() => _ProjectFormScreenState();
}

class _ProjectFormScreenState extends State<ProjectFormScreen> {
  final ProjectFormController controller = Get.put(ProjectFormController());

  String getAppBarTitle() {
    if (controller.currentStep.value == 1) return "New Project";
    if (controller.currentStep.value == 2) return "Add Team";
    return "Add Images";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor("EBEBEB"),
      appBar: AppBar(
        backgroundColor: HexColor("EBEBEB"),
        title: Obx(() => Text(getAppBarTitle(), style: const TextStyle(fontSize: 16))),
        leading: Icon<PERSON>utton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Get.back(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            children: [
              _buildCustomStepper(),
              Expanded(
                child: SingleChildScrollView(
                  child: Obx(() {
                    if (controller.currentStep.value == 1) {
                      return _buildAboutForm();
                    } else if (controller.currentStep.value == 2) {
                      return _buildTeamForm();
                    } else {
                      return _buildImagesForm();
                    }
                  }),
                ),
              ),
              _buildBottomNavigation(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomStepper() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Obx(() => Row(
            children: [
              _stepItem("About", 1),
              _line(),
              _stepItem("Team", 2),
              _line(),
              _stepItem("Images", 3),
            ],
          )),
        ),
        Container(
          margin: const EdgeInsets.only(top: 8),
          child: Divider(color: HexColor("ADADAD")),
        ),
      ],
    );
  }

  Widget _stepItem(String label, int stepNumber) {
    bool isActive = controller.currentStep.value == stepNumber;
    bool isCompleted = controller.currentStep.value > stepNumber;

    final Color borderColor = isActive || isCompleted ? Colors.blue : const Color(0xFFC6C6C6);
    final Color labelTextColor = isActive || isCompleted ? Colors.blue : Colors.black;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: borderColor),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(label, style: TextStyle(fontSize: 12, color: labelTextColor, fontWeight: FontWeight.w500)),
          const SizedBox(width: 6),
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: isActive || isCompleted ? Colors.blue : Colors.white,
              border: Border.all(color: borderColor),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(Icons.check, size: 14, color: Colors.white)
                  : Text(stepNumber.toString(),
                      style: TextStyle(
                        fontSize: 11,
                        color: isActive ? Colors.white : labelTextColor
                      )),
            ),
          ),
        ],
      ),
    );
  }

  Widget _line() {
    return Expanded(
      child: Container(
        height: 2,
        color: const Color(0xFFC6C6C6),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      color: HexColor("EBEBEB"),
      child: Column(
        children: [
          const Divider(thickness: 1),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      controller.clearTempState();
                      Get.back();
                    },
                    child: const Text("Cancel", style: TextStyle(fontSize: 12)),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      if (controller.currentStep.value == 3) {
                        controller.submitProject();
                      } else {
                        controller.nextStep();
                      }
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 14),
                      decoration: BoxDecoration(
                        color: const Color(0xFF383838),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      alignment: Alignment.center,
                      child: Obx(() => Text(
                        controller.currentStep.value == 3 ? "Submit" : "Save and Next",
                        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500),
                      )),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
          child: Text("Select Category", style: TextStyle(fontSize: 12)),
        ),
        Obx(() => Wrap(
          spacing: 4,
          runSpacing: 4,
          children: controller.categories.map((category) {
            final bool isSelected = controller.selectedCategory.value == category;
            return GestureDetector(
              onTap: () => controller.selectedCategory.value = category,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected ? HexColor("6F6F6F") : Colors.transparent,
                  border: Border.all(color: isSelected ? HexColor("6F6F6F") : Colors.grey),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  category,
                  style: TextStyle(
                    fontSize: 10,
                    color: isSelected ? Colors.white : Colors.grey,
                  ),
                ),
              ),
            );
          }).toList(),
        )),
        const SizedBox(height: 8),
        CustomTextField(label: "Project name", controller: controller.projectName),
        CustomTextField(label: "Location", controller: controller.location),
        _buildYearDropdown(),
        CustomTextField(label: "Project Duration", controller: controller.duration),
        CustomTextField(label: "Area", controller: controller.area),
        CustomTextField(label: "Project cost", controller: controller.cost, keyboardType: TextInputType.number),
        CustomTextField(label: "About", controller: controller.about),
      ],
    );
  }

 Widget _buildYearDropdown() {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 4.0),
    child: Obx(() => DropdownButtonFormField<int>(
      value: controller.selectedYear.value,
      decoration: InputDecoration(
        labelText: "Year",
        labelStyle: TextStyle(
          fontSize: 12,
          color: HexColor("555555"), // 👈 label color
        ),
        isDense: true,
        contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(6.0)),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(6.0),
          borderSide: BorderSide(color: HexColor("6F6F6F"),),
        ),
      ),
      items: List.generate(30, (index) {
        final year = DateTime.now().year - index;
        return DropdownMenuItem<int>(
          value: year,
          child: Text(
            year.toString(),
            style: TextStyle(
              fontSize: 12,
              color: HexColor("959595"), // 👈 item color
            ),
          ),
        );
      }),
      onChanged: (value) {
        if (value != null) {
          controller.selectedYear.value = value;
          controller.year.text = value.toString();
        }
      },
    )),
  );
}

  Widget _buildTeamForm() {
    return Obx(() {
      if (controller.showProfessionalSelection.value) {
        return _buildProfessionalSelector();
      } else if (controller.selectedProfessionals.isEmpty) {
        return _buildAddProfessionalsButton();
      } else {
        return _buildSelectedProfessionalsList();
      }
    });
  }

  Widget _buildSelectedProfessionalsList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: controller.selectedProfessionals.length,
          itemBuilder: (context, index) {
            final professional = controller.selectedProfessionals[index];
            return Row(
              children: [
                Expanded(
                  flex: 9,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 4),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: HexColor("#D8D8D8")),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(getProfessionalTitle(professional), style: const TextStyle(fontSize: 10, color: Colors.grey)),
                        const SizedBox(height: 4),
                        Text(professional, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                      ],
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.delete_outline, color: Colors.grey),
                  onPressed: () => controller.selectedProfessionals.removeAt(index),
                ),
              ],
            );
          },
        ),
        Center(
          child: TextButton(
            onPressed: () => controller.showProfessionalSelection.value = true,
            child: const Text('+ Add more professionals', style: TextStyle(color: Colors.blue)),
          ),
        ),
      ],
    );
  }

  Widget _buildProfessionalSelector() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          children: [
            Center(
              child: Container(
                width: 40,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.arrow_back_ios, size: 14, color: Colors.grey),
                    onPressed: () => controller.showProfessionalSelection.value = false,
                  ),
                  const Expanded(
                    child: Center(
                      child: Text("Choose user", style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
                    ),
                  ),
                  const SizedBox(width: 22),
                ],
              ),
            ),
            ...controller.professionalOptions.map((professional) {
              final isLast = professional == controller.professionalOptions.last;
              return Column(
                children: [
                  CheckboxListTile(
                    dense: true,
                    title: Text(
                      professional,
                      style: const TextStyle(fontSize: 13),
                      ),
                      value: controller.selectedProfessionals.contains(professional),
                      activeColor: HexColor("6F6F6F"), // 👈 Tick color (when checked)
                      checkColor: Colors.white,
                      onChanged: (isSelected) {
                        if (isSelected == true) {
                           controller.selectedProfessionals.add(professional);
                            } else {
                              controller.selectedProfessionals.remove(professional); }
                              },),
                               if (!isLast)
                    Divider(thickness: 1, color: Colors.grey.shade200, height: 0),
                ],
              );
            }).toList(),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.showProfessionalSelection.value = false,
              style: ElevatedButton.styleFrom(backgroundColor: const Color(0xFF383838)),
              child: const Text('Done', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

   Widget _buildAddProfessionalsButton() {
    return Center(
      child: Column(
        children: [
          IconButton(
            onPressed: () => controller.showProfessionalSelection.value = true,
            icon: const Icon(Icons.add, size: 24),
            color: HexColor("#0C0C0C"),
          ),
          const SizedBox(height: 4),
          Text(
            "Click on plus to add professionals",
            style: TextStyle(fontSize: 12, color: HexColor("#6F6F6F")),
          ),
        ],
      ),
    );
  }

  Widget _buildImagesForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(() {
          if (controller.selectedImages.isEmpty) {
            return GestureDetector(
              onTap: controller.pickImages,
              child: Container(
                height: 350,
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: HexColor("F4F3F3"),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_circle, size: 20, color: Colors.white),
                      //Utils.pngImage(AssetStrings.AddImageIcon),
                      const SizedBox(height: 8),
                      const Text("Add image", style: TextStyle(color: Colors.grey, fontSize: 12)),
                    ],
                  ),
                ),
              ),
            );
          } else {
            return Column(
              children: [
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: controller.selectedImages.length,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 4,
                    mainAxisSpacing: 4,
                  ),
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: FileImage(controller.selectedImages[index]),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: GestureDetector(
                            onTap: () => controller.selectedImages.removeAt(index),
                            child: Container(
                              decoration: const BoxDecoration(
                                color: Colors.black54,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(Icons.close, color: Colors.white, size: 16),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 8),

                GestureDetector(
                  onTap: controller.pickImages,
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    decoration: BoxDecoration(
                      color: HexColor("F5F5F5"),
                      border: Border.all(color: HexColor("C6C6C6")),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                         Text("Add more images", style: TextStyle(fontSize: 12, color: Colors.black)),
                            SizedBox(width: 6),
                        Icon(Icons.add, size: 20, color: Colors.black),


                      ],
                    ),
                  ),
                ),
              ],
            );
          }
        }),
          if (controller.selectedImages.isEmpty)
        const SizedBox(height: 8),
          if (controller.selectedImages.isEmpty)
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 12),
          child: Text("Select up to 8 images, images can be of maximum 12MB.", style: TextStyle(fontSize: 12, color: Colors.grey)),
        ),
      ],
    );
  }

  String getProfessionalTitle(String name) {
    if (name.contains('Designer')) return 'Designer';
    if (name.contains('Contractor')) return 'Contractor';
    return 'Professional';
  }
}

