import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controller/setting_controller.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsController controller = Get.put(SettingsController());

  final List<Map<String, dynamic>> settingItems = [
    {"icon": Icons.person_outline, "title": "Personal information"},
    {"icon": Icons.lock_outline, "title": "Password and security"},
    {"icon": Icons.account_box_outlined, "title": "Account type"},
    {"icon": Icons.translate, "title": "Translation"},
    {"icon": Icons.notifications_none, "title": "Notifications"},
    {"icon": Icons.privacy_tip_outlined, "title": "Privacy and sharing"},
    {"icon": Icons.help_outline, "title": "Help"},
    {"icon": Icons.person_add_alt, "title": "Add account"},
  ];

  final Set<String> dividerAfterTitles = {
    "Personal information",
    "Account type",
    "Notifications",
    "Help"
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("SETTINGS", style: TextStyle(fontWeight: FontWeight.w600,fontSize: 18)),
        leading: const BackButton(),
        foregroundColor: Colors.grey,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(15.0),
            child: SizedBox(
              height: 40,
              child: TextField(
                decoration: InputDecoration(
                  hintText: 'Search',
                   prefixIcon: const Icon(Icons.search),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: const EdgeInsets.symmetric(horizontal: 8),
                ),
              ),
            ),
          ),

          // Settings List
          Expanded(
            child: ListView.builder(
              itemCount: settingItems.length + 1,
              itemBuilder: (context, index) {
                if (index == settingItems.length) {
                  return ListTile(
                    dense: true,
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                    leading: const Icon(Icons.logout, color: Colors.red),
                    title: const Text("Logout", style: TextStyle(color: Colors.red)),
                    onTap: controller.logout,
                  );
                }
                final item = settingItems[index];
                final title = item['title'];

                return Column(
                  children: [
                    ListTile(
                      dense: true,
                      visualDensity: const VisualDensity(vertical: -2),
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16),
                      leading: Icon(item['icon']),
                      title: Text(title),
                      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                      onTap: () {
                        // Navigate or trigger action
                      },
                    ),
                    if (dividerAfterTitles.contains(title)) const Divider(height: 1),
                  ],
                );
              },
            ),
          ),
        ],
      ),

    );
  }
}
