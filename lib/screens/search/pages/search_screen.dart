import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../constants/assets.dart';
import '../../../utils/utils.dart';
import '../controller/search_controller.dart';

class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  late SearchPageController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(SearchPageController());
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final topImageHeight = screenHeight * 0.5;

    return Scaffold(
      backgroundColor: HexColor.fromHex("E5E5E5"),
      resizeToAvoidBottomInset: true,
      body: SafeArea(
        child: SingleChildScrollView(
          reverse: true,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  Utils.pngImage(AssetStrings.SearchImageIcon),
                  Positioned(
                    top: topImageHeight * 0.3,
                    left: 8,
                    right: 0,
                    child: Column(
                      children: const [
                        Text(
                          'Hello, MuseLab !',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.w400,
                            color: Colors.black,
                          ),
                        ),
                        SizedBox(height: 8),
                        Text(
                          'What can I help with?',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w300,
                            color: Colors.black54,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Search Field
                    Container(
                      height: 40,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      child: TextField(
                        controller: controller.textController,
                        onSubmitted: (value) => controller.addRecentSearch(value),
                        decoration: InputDecoration(
                          hintText: 'Search or Ask OpenSlate',
                          prefixIcon: const Icon(Icons.search, size: 18),
                          isDense: true,
                          contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          filled: true,
                          fillColor: Colors.white.withOpacity(0.8),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide.none,
                          ),
                        ),
                        style: const TextStyle(fontSize: 14),
                      ),

                    ),
                    const SizedBox(height: 15),

                    // Recent Searches
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Recent Searches',
                          style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                        ),
                        GestureDetector(
                          onTap: controller.clearHistory,
                          child: const Text(
                            'Clear History',
                            style: TextStyle(fontSize: 12, color: Colors.blue),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 5),
                    Obx(() => Wrap(
                      spacing: 2,
                      children: controller.recentSearches.map((search) {
                        return Chip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.watch_later_outlined, color: HexColor("9EBBE7"), size: 14),
                              const SizedBox(width: 4),
                              Text(search, style: const TextStyle(fontSize: 10)),
                            ],
                          ),
                          backgroundColor: Colors.white.withOpacity(0.8),
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                        );
                      }).toList(),
                    )),

                    const SizedBox(height: 10),

                    // Trending Searches
                    const Text(
                      'Trending Searches',
                      style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(height: 5),
                    Obx(() => Wrap(
                      spacing: 3,
                      children: controller.trendingSearches.map((search) {
                        return Chip(
                          label: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.trending_up, color: HexColor("9EBBE7"), size: 14),
                              const SizedBox(width: 4),
                              Text(search, style: const TextStyle(fontSize: 10)),
                            ],
                          ),
                          backgroundColor: Colors.white.withOpacity(0.8),
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                        );
                      }).toList(),
                    )),
                  ],
                ),
              ),
              const SizedBox(height: 100), // Prevent keyboard from covering bottom content
            ],
          ),
        ),
      ),
    );
  }
}
