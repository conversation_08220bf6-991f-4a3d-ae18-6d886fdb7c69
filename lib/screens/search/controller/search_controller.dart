// search_controller.dart
import 'package:get/get.dart';
import 'package:flutter/material.dart';

class SearchPageController extends GetxController {
  var recentSearches = <String>[].obs;
  var trendingSearches = <String>[].obs;
  final textController = TextEditingController(); // ✅ Controller for search field

  @override
  void onInit() {
    super.onInit();
    fetchSearchData();
  }

  void fetchSearchData() {
    recentSearches.value = [
      'Marble Vendor in...',
      'Plumber in Mumbai',
      'Minimal Sofa',
    ];
    trendingSearches.value = [
      'Venzo',
      'Costa Mia',
      'Asian Paints',
      'Zia',
      'Godrej Design week',
    ];
  }

  void clearHistory() {
    recentSearches.clear();
  }

  void addRecentSearch(String query) {
    final trimmed = query.trim();
    if (trimmed.isEmpty) return;

    recentSearches.remove(trimmed);
    recentSearches.insert(0, trimmed);

    if (recentSearches.length > 10) {
      recentSearches.removeLast();
    }

    textController.clear(); // ✅ Clear the input after submission
  }

  @override
  void onClose() {
    textController.dispose(); // ✅ Dispose when controller is destroyed
    super.onClose();
  }
}
