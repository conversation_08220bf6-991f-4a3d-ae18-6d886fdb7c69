import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../constants/colors.dart';
import '../../../services/connectivity_service.dart';
import '../../../services/responsive_service.dart';

class NoInternetScreen extends StatelessWidget {
  const NoInternetScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final connectivityService = ConnectivityService.to;
    final responsiveService = ResponsiveService.to;

    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      body: SafeArea(
        child: Obx(() {
          responsiveService.updateScreenInfo();
          
          return Center(
            child: Padding(
              padding: responsiveService.responsivePadding(
                mobile: const EdgeInsets.all(24.0),
                tablet: const EdgeInsets.all(32.0),
                desktop: const EdgeInsets.all(48.0),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // No Internet Icon
                  Container(
                    width: responsiveService.responsiveValue(
                      mobile: 120.0,
                      tablet: 150.0,
                      desktop: 180.0,
                    ),
                    height: responsiveService.responsiveValue(
                      mobile: 120.0,
                      tablet: 150.0,
                      desktop: 180.0,
                    ),
                    decoration: BoxDecoration(
                      color: ColorConstants.lightGrey,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.wifi_off_rounded,
                      size: responsiveService.responsiveValue(
                        mobile: 60.0,
                        tablet: 75.0,
                        desktop: 90.0,
                      ),
                      color: ColorConstants.gunSmoke,
                    ),
                  ),
                  
                  SizedBox(height: responsiveService.responsiveValue(
                    mobile: 32.0,
                    tablet: 40.0,
                    desktop: 48.0,
                  )),
                  
                  // Title
                  Text(
                    'No Internet Connection',
                    style: GoogleFonts.poppins(
                      fontSize: responsiveService.responsiveFontSize(24),
                      fontWeight: FontWeight.w600,
                      color: ColorConstants.darkBlack,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: responsiveService.responsiveValue(
                    mobile: 16.0,
                    tablet: 20.0,
                    desktop: 24.0,
                  )),
                  
                  // Description
                  Text(
                    'Please check your internet connection\nand try again',
                    style: GoogleFonts.poppins(
                      fontSize: responsiveService.responsiveFontSize(16),
                      fontWeight: FontWeight.w400,
                      color: ColorConstants.gunSmoke,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: responsiveService.responsiveValue(
                    mobile: 48.0,
                    tablet: 56.0,
                    desktop: 64.0,
                  )),
                  
                  // Retry Button
                  SizedBox(
                    width: responsiveService.getCardWidth(),
                    height: responsiveService.responsiveValue(
                      mobile: 50.0,
                      tablet: 55.0,
                      desktop: 60.0,
                    ),
                    child: Obx(() => ElevatedButton(
                      onPressed: connectivityService.isConnected.value
                          ? null
                          : () async {
                              await connectivityService.retryConnection();
                            },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorConstants.deepBlue,
                        foregroundColor: ColorConstants.white,
                        elevation: 2,
                        shadowColor: ColorConstants.deepBlue.withValues(alpha: 0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.refresh_rounded,
                            size: responsiveService.responsiveValue(
                              mobile: 20.0,
                              tablet: 22.0,
                              desktop: 24.0,
                            ),
                          ),
                          SizedBox(width: responsiveService.responsiveValue(
                            mobile: 8.0,
                            tablet: 10.0,
                            desktop: 12.0,
                          )),
                          Text(
                            'Try Again',
                            style: GoogleFonts.poppins(
                              fontSize: responsiveService.responsiveFontSize(16),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    )),
                  ),
                  
                  SizedBox(height: responsiveService.responsiveValue(
                    mobile: 24.0,
                    tablet: 28.0,
                    desktop: 32.0,
                  )),
                  
                  // Connection Status
                  Obx(() => Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: responsiveService.responsiveValue(
                        mobile: 16.0,
                        tablet: 20.0,
                        desktop: 24.0,
                      ),
                      vertical: responsiveService.responsiveValue(
                        mobile: 8.0,
                        tablet: 10.0,
                        desktop: 12.0,
                      ),
                    ),
                    decoration: BoxDecoration(
                      color: connectivityService.isConnected.value
                          ? ColorConstants.brightGreen.withValues(alpha: 0.1)
                          : ColorConstants.gunSmoke.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: connectivityService.isConnected.value 
                            ? ColorConstants.brightGreen
                            : ColorConstants.gunSmoke,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          connectivityService.isConnected.value 
                              ? Icons.wifi_rounded
                              : Icons.wifi_off_rounded,
                          size: responsiveService.responsiveValue(
                            mobile: 16.0,
                            tablet: 18.0,
                            desktop: 20.0,
                          ),
                          color: connectivityService.isConnected.value 
                              ? ColorConstants.brightGreen
                              : ColorConstants.gunSmoke,
                        ),
                        SizedBox(width: responsiveService.responsiveValue(
                          mobile: 8.0,
                          tablet: 10.0,
                          desktop: 12.0,
                        )),
                        Text(
                          connectivityService.isConnected.value 
                              ? 'Connected'
                              : 'Disconnected',
                          style: GoogleFonts.poppins(
                            fontSize: responsiveService.responsiveFontSize(14),
                            fontWeight: FontWeight.w500,
                            color: connectivityService.isConnected.value 
                                ? ColorConstants.brightGreen
                                : ColorConstants.gunSmoke,
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
}
