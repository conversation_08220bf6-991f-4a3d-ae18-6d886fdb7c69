 import 'package:get/get.dart';

class AuthController extends GetxController {
  var email = ''.obs;
  var password = ''.obs;
  var confirmPassword = ''.obs;
  var otpCode = ''.obs;

  var isPasswordVisible = false.obs;
  var isConfirmPasswordVisible = false.obs;

  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  void toggleConfirmPasswordVisibility() {
    isConfirmPasswordVisible.value = !isConfirmPasswordVisible.value;
  }

  void signIn() {
    // TODO: Integrate your API here
  }

  void sendResetLink() {
    // TODO: Integrate your API here
  }

  void verifyOtp() {
    // TODO: Integrate your API here
  }

  void resetPassword() {
    // TODO: Integrate your API here
  }
}
