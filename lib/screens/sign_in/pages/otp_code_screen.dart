import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/sign_in/controller/sign_in_controller.dart';
import 'package:openslate/screens/sign_in/pages/new_password_screen.dart';
import 'package:openslate/utils/utils.dart'; // For HexColor

class OtpCodeScreen extends StatefulWidget {
  @override
  State<OtpCodeScreen> createState() => _OtpCodeScreenState();
}

class _OtpCodeScreenState extends State<OtpCodeScreen> {
  final controller = Get.find<AuthController>();
  final List<TextEditingController> otpControllers =
      List.generate(6, (_) => TextEditingController());

  void _onOtpChange() {
    String code = otpControllers.map((c) => c.text).join();
    controller.otpCode.value = code;

    if (code.length == 6) {
      if (code == "111111") {
        Get.to(() => NewPasswordScreen());
      } else {
        // Optional: show error toast/snackbar if wrong code
        Get.snackbar(
          'Invalid Code',
          'The code you entered is incorrect.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.redAccent,
          colorText: Colors.white,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Enter code'),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'Enter code sent to your email\<EMAIL> to reset your\npassword',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 14),
              ),
            ),
            SizedBox(height: 30),

            // OTP Boxes
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: List.generate(6, (index) {
                return SizedBox(
                  width: 45,
                  child: TextField(
                    controller: otpControllers[index],
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    maxLength: 1,
                    cursorColor: HexColor("6F6F6F"),
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    decoration: InputDecoration(
                      counterText: '',
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 12),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(color: HexColor("D8D8D8")),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                        borderSide: BorderSide(color: HexColor("6F6F6F"), width: 1.5),
                      ),
                    ),
                    onChanged: (value) {
                      if (value.length == 1 && index < 5) {
                        FocusScope.of(context).nextFocus();
                      } else if (value.isEmpty && index > 0) {
                        FocusScope.of(context).previousFocus();
                      }
                      _onOtpChange();
                    },
                  ),
                );
              }),
            ),
          ],
        ),
      ),
    );
  }
}
