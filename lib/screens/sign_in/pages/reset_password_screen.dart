import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/sign_in/controller/sign_in_controller.dart';
import 'package:openslate/screens/sign_in/pages/otp_code_screen.dart';
import 'package:openslate/utils/utils.dart';

class ResetPasswordScreen extends StatefulWidget {
  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final controller = Get.find<AuthController>();
  final TextEditingController emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Reset password'),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start, // 👈 Rest content start from left
          children: [
            Center( // 👈 Center only the top text
              child: Text(
                'Enter your email or username and we\'ll\nsend you a link to reset your \npassword.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 12,color: HexColor("#6F6F6F")),
              ),
            ),
            SizedBox(height: 25),
            // Email TextField with outline border and focused border
            TextFormField(
              controller: emailController,
              keyboardType: TextInputType.emailAddress,
              cursorColor: HexColor("6F6F6F"),
              style: TextStyle(fontSize: 12),
              decoration: InputDecoration(
                labelText: 'Email address',
                labelStyle: TextStyle(
                  fontSize: 12,
                  color: HexColor("555555"),
                ),
                isDense: true,
                contentPadding: EdgeInsets.symmetric(vertical: 12, horizontal: 15),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(color: HexColor("D8D8D8")),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                  borderSide: BorderSide(color: HexColor("D8D8D8")),
                ),
              ),
            ),
            SizedBox(height: 20),

            // Send Link Button
            ElevatedButton(
              onPressed: () {
                controller.email.value = emailController.text;
                controller.sendResetLink();
                Get.to(() => OtpCodeScreen());
              },
              child: Text('Send link', style: TextStyle(color: Colors.white,fontWeight: FontWeight.bold)),
              style: ElevatedButton.styleFrom(
                backgroundColor: HexColor("383838"),
                minimumSize: Size(double.infinity, 40),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
