import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/sign_in/controller/sign_in_controller.dart';
import 'package:openslate/screens/sign_in/pages/reset_password_screen.dart';
import 'package:openslate/utils/utils.dart';

class SignInScreen extends StatelessWidget {
  final controller = Get.put(AuthController());
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(20),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 80),
              Text('Sign in', style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold)),
              <PERSON>zed<PERSON><PERSON>(height: 20),
              
              CommonTextField(
                controller: emailController,
                label: 'Email address',
              ),

              Obx(() => CommonTextField(
                controller: passwordController,
                label: 'Password',
                obscureText: !controller.isPasswordVisible.value,
                suffixIcon: Icon(
                  controller.isPasswordVisible.value ? Icons.visibility : Icons.visibility_off,color: HexColor("959595"),
                ),
                onSuffixTap: controller.togglePasswordVisibility,
              )),

              Align(
                alignment: Alignment.centerLeft,
                child: TextButton(
                  onPressed: () => Get.to(() => ResetPasswordScreen()),
                  child: Text('Forgot password?',style: TextStyle(color: HexColor("#383838")),),
            
                ),
              ),
              ElevatedButton(
  onPressed: () {
    controller.email.value = emailController.text;
    controller.password.value = passwordController.text;
    controller.signIn();
  },
  child: Text('Sign in', style: TextStyle(color: Colors.white,fontWeight: FontWeight.bold)),
  style: ElevatedButton.styleFrom(
    backgroundColor: HexColor("#383838"),
    minimumSize: Size(double.infinity, 40),
    shape: RoundedRectangleBorder( 
      borderRadius: BorderRadius.circular(6),
    ),
  ),
),
              SizedBox(height: 20),
              ElevatedButton.icon(
  onPressed: () {},
  icon: Icon(Icons.apple),
  label: Text('Continue with Apple',style: TextStyle(color: HexColor("383838"),fontSize: 13),),
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.white,
    minimumSize: Size(double.infinity, 50),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(6),
    ),
  ),
),
              SizedBox(height: 10),
ElevatedButton.icon(
  onPressed: () {},
  icon: Icon(Icons.g_mobiledata),
  label: Text('Continue with Google',style: TextStyle(color: HexColor("383838"),fontSize: 13),),
  style: ElevatedButton.styleFrom(
    backgroundColor: Colors.white,
    minimumSize: Size(double.infinity, 50),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(6),
    ),
  ),
),
SizedBox(height: 20),
Text('New to OpenSite? Register', style: TextStyle(fontSize: 12,color: HexColor('383838'),fontWeight: FontWeight.bold)),
SizedBox(height: 10),
Text('By Joining, you agree with our', style: TextStyle(fontSize: 11,color: HexColor('6F6F6F'),fontWeight: FontWeight.w300)),
Text('Terms of Services and  Privacy Policy.', style: TextStyle(fontSize: 11,color: HexColor('6F6F6F'),fontWeight: FontWeight.w300)),


            ],
          ),
        ),
      ),
    );
  }
}

class CommonTextField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final TextInputType keyboardType;
  final bool obscureText;
  final Widget? suffixIcon;
  final VoidCallback? onSuffixTap;

  const CommonTextField({
    Key? key,
    required this.controller,
    required this.label,
    this.keyboardType = TextInputType.text,
    this.obscureText = false,
    this.suffixIcon,
    this.onSuffixTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        cursorColor: HexColor("6F6F6F"),
        style: const TextStyle(fontSize: 14),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: TextStyle(
            fontSize: 12,
            color: HexColor("555555"),
          ),
          isDense: true,
          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 15),
          suffixIcon: suffixIcon != null
              ? IconButton(
                  icon: suffixIcon!,
                  onPressed: onSuffixTap,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
             borderSide: BorderSide(color: HexColor("D8D8D8"))
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8.0),
            borderSide: BorderSide(color: HexColor("D8D8D8"),),
          ),
        ),
      ),
    );
  }
}

