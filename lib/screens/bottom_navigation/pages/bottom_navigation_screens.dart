
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../constants/assets.dart';
import '../../../constants/colors.dart';
import '../../../utils/tags.dart';
import '../../../utils/utils.dart';
import '../controller/bottom_navigation_controller.dart';
import '../widgets/blurred_overlay_widget.dart';

class BottomNavigationScreen extends StatefulWidget {
  const BottomNavigationScreen({super.key});

  @override
  State<BottomNavigationScreen> createState() => _BottomNavigationScreenState();
}

class _BottomNavigationScreenState extends State<BottomNavigationScreen> {
  final BottomNavigationController controller =
  Get.put(BottomNavigationController(), tag: Tag.bottomNavigationTag);

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // prevents auto pop; logic is handled in onPopInvoked
      onPopInvoked: (didPop) {
        if (didPop) return; // A system pop (like swipe back) already happened

        int currentIndex = controller.selectedIndex.value;
        final currentNavigator = controller.navigatorKeys[currentIndex].currentState;

        if (currentNavigator != null && currentNavigator.canPop()) {
          currentNavigator.pop();
          return;
        }

        if (currentIndex != 0) {
          controller.changeIndex(0);
          return;
        }

        // Already at home root – exit app or show dialog if you want
      },
      child: Scaffold(
        body: Stack(
          children: [
            // Use IndexedStack with the navigator tabs instead of simple screens
            Obx(() => IndexedStack(
              index: controller.selectedIndex.value,
              children: controller.tabs,
            )),
            Obx(() => controller.showOverlay.value
                ? BlurredOverlayWidget()
                : const SizedBox()),
          ],
        ),
        bottomNavigationBar: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: const BoxDecoration(
            color: ColorConstants.bottomNavigationColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 5,
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildAddIcon(
                AssetStrings.bottomProfileImage,
                AssetStrings.bottomProfileImage,
                0,
              ),
              _buildAddIcon(
                AssetStrings.bottomHomeImage,
                AssetStrings.bottomInactiveHomeImage,
                1,
              ),
              _buildAddIcon(
                "${AssetStrings.assetImagesBottomNavigation}my-space.png",
                "${AssetStrings.assetImagesBottomNavigation}my-space1.png",
                2,
              ),
              _buildAddIcon(
                AssetStrings.bottomInactiveSearchImage,
                AssetStrings.bottomSearchImage,
                3,
              ),
              _buildAddIcon(
                AssetStrings.bottomGroupImage,
                AssetStrings.bottomGroupImage,
                4,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddIcon(String activeImage, String inactiveImage, int index) {
    return GestureDetector(
      onTap: () => controller.changeIndex(index),
      child: Obx(() {
        final isSelected = controller.selectedIndex.value == index;
        return Utils.pngImage(isSelected ? inactiveImage : activeImage);
      }),
    );
  }

}