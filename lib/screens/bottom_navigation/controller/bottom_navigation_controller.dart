import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/designers/pages/designer_detail_screen.dart';
import 'package:openslate/screens/designers/pages/designer_list_screen.dart';
import 'package:openslate/screens/projects/pages/project_detail_screen.dart';
import 'package:openslate/screens/projects/pages/project_list_screen.dart';
import 'package:openslate/screens/projects/pages/projects_sub_list_screen.dart';
import 'package:openslate/screens/search/pages/search_screen.dart';
import '../../home/<USER>/home_screen.dart';
import '../../my_space/pages/my_space_screen.dart';
import '../../profile/pages/dummy_profile_screen.dart';
import '../../projects/pages/projects_sub_item_screen.dart';
import '../../setting/pages/setting_screen.dart';

class BottomNavigationController extends GetxController {
  var selectedIndex = 0.obs;
  var showOverlay = false.obs;

  // Navigator keys for nested navigation
  final List<GlobalKey<NavigatorState>> navigator<PERSON>eys = [
    Get.nestedKey(1)!,
    Get.nestedKey(2)!,
    Get.nestedKey(3)!,
    Get.nestedKey(4)!,
  ];

  // Cache tabs to avoid recreating them on every access
  late final List<Widget> _tabs;

  @override
  void onInit() {
    super.onInit();
    _initializeTabs();
    if (Get.arguments != null && Get.arguments['index'] != null) {
      selectedIndex.value = Get.arguments['index'];
    }
  }

  void _initializeTabs() {
    _tabs = [
      _buildProfileTab(),
      _buildHomeTab(),
      _buildMySpaceTab(),
      _buildSearchTab(),
    ];
  }

  List<Widget> get tabs => _tabs;

  Widget _buildProfileTab() {
    return Navigator(
      key: navigatorKeys[0],
      initialRoute: '/ProfileCheckScreen',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/ProfileCheckScreen':
            return GetPageRoute(
              settings: settings,
              page: () => SettingsScreen(),
            );
          default:
            return GetPageRoute(
              settings: settings,
              page: () => SettingsScreen(),
            );
        }
      },
    );
  }

  Widget _buildHomeTab() {
    return Navigator(
      key: navigatorKeys[1],
      initialRoute: '/home',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/home':
            return GetPageRoute(
              settings: settings,
              page: () => const HomeScreen(),
            );
          case '/mySpaceScreen':
            return GetPageRoute(
              settings: settings,
              page: () => MySpaceScreen(),
            );
          case '/projectListScreen':
            return GetPageRoute(
              settings: settings,
              page: () => ProjectListScreen(),
            );
          case '/projectsSubListScreen':
            return GetPageRoute(
              settings: settings,
              page: () => ProjectsSubListScreen(),
            );
          case '/projectsSubItemScreen':
            return GetPageRoute(
              settings: settings,
              page: () => ProjectsSubItemScreen(),
            );
          case '/projectsDetailScreen':
            return GetPageRoute(
              settings: settings,
              page: () => ProjectDetailScreen(),
            );
          case '/designerListScreen':
            return GetPageRoute(
              settings: settings,
              page: () => DesignerListScreen(),
            );
          case '/designerDetailScreen':
            return GetPageRoute(
              settings: settings,
              page: () => DesignerDetailScreen(),
            );
          default:
            return GetPageRoute(
              settings: settings,
              page: () => const HomeScreen(),
            );
        }
      },
    );
  }

  Widget _buildMySpaceTab() {
    return Navigator(
      key: navigatorKeys[2],
      initialRoute: '/mySpace',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/mySpace':
            return GetPageRoute(
              settings: settings,
              page: () => MySpaceScreen(),
            );
          default:
            return GetPageRoute(
              settings: settings,
              page: () => MySpaceScreen(),
            );
        }
      },
    );
  }

  Widget _buildSearchTab() {
    return Navigator(
      key: navigatorKeys[3],
      initialRoute: '/searchScreen',
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case '/searchScreen':
            return GetPageRoute(
              settings: settings,
              page: () => SearchScreen(),
            );
          default:
            return GetPageRoute(
              settings: settings,
              page: () => SearchScreen(),
            );
        }
      },
    );
  }

  void changeIndex(int index) {
    if (index == 4) {
      showOverlay.value = true;
    } else {
      if (index == selectedIndex.value) {
        navigatorKeys[index].currentState?.popUntil((route) => route.isFirst);
      } else {
        selectedIndex.value = index;
      }
      showOverlay.value = false;
    }
  }
}
