import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/add_new_project_form/pages/add_new_project_form_screen.dart';

class BlurredOverlayWidget extends StatelessWidget {
  final VoidCallback? onTapOutside;

  const BlurredOverlayWidget({Key? key, this.onTapOutside}) : super(key: key);

  Widget _buildOption(IconData icon, String label) {
    return Container(
      //padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      height: 80,
      width: 80,
      decoration: BoxDecoration(
        color: Colors.lightBlue[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 20, color: Colors.black),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.black,
              fontSize: 12
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapOutside, // Optional callback to dismiss overlay
      child: Container(
        color: Colors.black.withOpacity(0.5),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 1),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(16),
                        bottomLeft: Radius.circular(16),
                      ),
                    ),
                    child:  RotatedBox(
                      quarterTurns: 3,
                      child: InkWell(
                       onTap: () {
                       Get.to(() => ProjectFormScreen());

                       },
                        child: Text(
                          "Add New +",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildOption(Icons.dashboard, "PROJECTS"),
                          SizedBox(width: 5,),
                          _buildOption(Icons.favorite_border, "POST"),
                          SizedBox(width: 5,),
                          _buildOption(Icons.inventory_2_outlined, "PRODUCTS"),
                          SizedBox(width: 5,),
                          _buildOption(Icons.reviews_outlined, "REVIEWS"),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
