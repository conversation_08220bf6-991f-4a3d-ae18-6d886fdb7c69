import 'package:flutter/material.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';

class SmartImageWidget extends StatefulWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final BorderRadius? borderRadius;
  final Color backgroundColor;

  const SmartImageWidget({
    Key? key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit,
    this.borderRadius,
    this.backgroundColor = Colors.white,
  }) : super(key: key);

  @override
  State<SmartImageWidget> createState() => _SmartImageWidgetState();
}

class _SmartImageWidgetState extends State<SmartImageWidget> {
  bool _isLoading = true;
  bool _hasError = false;
  double? _imageAspectRatio;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: widget.backgroundColor,
        borderRadius: widget.borderRadius,
      ),
      child: ClipRRect(
        borderRadius: widget.borderRadius ?? BorderRadius.zero,
        child: Stack(
          children: [
            // Background container to prevent flash
            Container(
              width: double.infinity,
              height: double.infinity,
              color: widget.backgroundColor,
            ),
            // Image
            FastCachedImage(
              url: widget.imageUrl,
              fit: _determineFit(),
              width: widget.width,
              height: widget.height,
              loadingBuilder: (context, progress) {
                return Container(
                  width: widget.width,
                  height: widget.height,
                  color: widget.backgroundColor,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: Colors.grey[400],
                      strokeWidth: 2,
                    ),
                  ),
                );
              },
              errorBuilder: (context, exception, stacktrace) {
                return Container(
                  width: widget.width,
                  height: widget.height,
                  color: widget.backgroundColor,
                  child: Icon(
                    Icons.error_outline,
                    color: Colors.grey[400],
                    size: 40,
                  ),
                );
              },
              fadeInDuration: const Duration(milliseconds: 200),
            ),
          ],
        ),
      ),
    );
  }

  BoxFit _determineFit() {
    if (widget.fit != null) {
      return widget.fit!;
    }
    
    // Default to cover for full container fill
    return BoxFit.cover;
  }
}
