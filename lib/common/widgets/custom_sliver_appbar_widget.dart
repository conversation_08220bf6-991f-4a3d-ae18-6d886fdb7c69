import 'dart:math';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../screens/projects/widgets/projects_category_button_widget.dart';
import '../../utils/utils.dart';


class CustomSliverAppBar extends StatelessWidget {
  final String title;
  final String backgroundImage;
  final List<String> categories;
  final void Function(String category) onCategoryTap;
  final int itemsPerRow;
  final double expandedHeight;

  const CustomSliverAppBar({
    Key? key,
    required this.title,
    required this.backgroundImage,
    required this.categories,
    required this.onCategoryTap,
    this.itemsPerRow = 3,
    this.expandedHeight = 340,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final rowCount = (categories.length / itemsPerRow).ceil();

    return SliverAppBar(
      leading: IconButton(
        onPressed: () => Navigator.pop(context),
        icon: const Icon(Icons.arrow_back_ios),
      ),
      title: Text(title),
      expandedHeight: expandedHeight,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          children: [
            // Background Image
            Positioned.fill(
              child: Utils.pngImage(backgroundImage),
            ),

            // Category Buttons
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 150,
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                color: Colors.black.withOpacity(0.5),
                child: SizedBox(
                  height: 150,
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(rowCount, (rowIndex) {
                        final startIndex = rowIndex * itemsPerRow;
                        final endIndex = min(startIndex + itemsPerRow, categories.length);
                        final rowItems = categories.sublist(startIndex, endIndex);

                        return Padding(
                          padding: const EdgeInsets.only(bottom: 10),
                          child: Row(
                            children: rowItems.map((category) {
                              return ProjectCategoryButton(
                                category: category,
                                showColorBox: false,
                                onTap: () => onCategoryTap(category),
                              );
                            }).toList(),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
