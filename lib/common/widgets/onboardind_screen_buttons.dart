import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommonButtons {
  // Get Started Button
  static Widget getStarted({
    required VoidCallback onPressed,
    double borderRadius = 8,
    Color backgroundColor = const Color(0xFF383838),
    Color textColor = Colors.white,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      child: Text(
        "Get started",
        style: TextStyle(color: textColor),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: const EdgeInsets.symmetric(vertical: 14),
      ),
    );
  }

  // Explore Button
  static Widget explore({
    required VoidCallback onPressed,
    double borderRadius = 8,
    Color borderColor = const Color(0xFF383838),
    Color textColor = const Color(0xFF383838),
    IconData icon = Icons.arrow_forward,
  }) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        side: BorderSide(color: borderColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        padding: const EdgeInsets.symmetric(vertical: 14),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text("Explore", style: TextStyle(color: textColor)),
          const SizedBox(width: 6),
          Icon(icon, color: textColor),
        ],
      ),
    );
  }
}
