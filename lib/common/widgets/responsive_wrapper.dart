import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../services/responsive_service.dart';

class ResponsiveWrapper extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final bool updateOnResize;

  const ResponsiveWrapper({
    super.key,
    required this.child,
    this.padding,
    this.updateOnResize = true,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveService = ResponsiveService.to;
    
    return LayoutBuilder(
      builder: (context, constraints) {
        if (updateOnResize) {
          responsiveService.updateScreenInfo();
        }
        
        return Container(
          padding: padding ?? responsiveService.responsivePadding(
            mobile: const EdgeInsets.all(16.0),
            tablet: const EdgeInsets.all(24.0),
            desktop: const EdgeInsets.all(32.0),
          ),
          child: child,
        );
      },
    );
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, DeviceType deviceType) builder;
  final Widget? mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveBuilder({
    super.key,
    required this.builder,
    this.mobile,
    this.tablet,
    this.desktop,
  });

  const ResponsiveBuilder.widgets({
    super.key,
    this.mobile,
    this.tablet,
    this.desktop,
  }) : builder = _defaultBuilder;

  static Widget _defaultBuilder(BuildContext context, DeviceType deviceType) {
    return const SizedBox.shrink();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final responsiveService = ResponsiveService.to;
      final deviceType = responsiveService.deviceType.value;
      
      if (mobile != null || tablet != null || desktop != null) {
        switch (deviceType) {
          case DeviceType.mobile:
            return mobile ?? tablet ?? desktop ?? const SizedBox.shrink();
          case DeviceType.tablet:
            return tablet ?? mobile ?? desktop ?? const SizedBox.shrink();
          case DeviceType.desktop:
            return desktop ?? tablet ?? mobile ?? const SizedBox.shrink();
        }
      }
      
      return builder(context, deviceType);
    });
  }
}

class ResponsiveText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final double baseFontSize;
  final FontWeight? fontWeight;
  final Color? color;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;

  const ResponsiveText(
    this.text, {
    super.key,
    this.style,
    this.baseFontSize = 16.0,
    this.fontWeight,
    this.color,
    this.textAlign,
    this.maxLines,
    this.overflow,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveService = ResponsiveService.to;
    
    return Obx(() {
      final fontSize = responsiveService.responsiveFontSize(baseFontSize);
      
      return Text(
        text,
        style: style?.copyWith(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
        ) ?? TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          color: color,
        ),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: overflow,
      );
    });
  }
}

class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? mobileWidth;
  final double? tabletWidth;
  final double? desktopWidth;
  final double? mobileHeight;
  final double? tabletHeight;
  final double? desktopHeight;
  final EdgeInsets? mobilePadding;
  final EdgeInsets? tabletPadding;
  final EdgeInsets? desktopPadding;
  final EdgeInsets? mobileMargin;
  final EdgeInsets? tabletMargin;
  final EdgeInsets? desktopMargin;
  final Decoration? decoration;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.mobileWidth,
    this.tabletWidth,
    this.desktopWidth,
    this.mobileHeight,
    this.tabletHeight,
    this.desktopHeight,
    this.mobilePadding,
    this.tabletPadding,
    this.desktopPadding,
    this.mobileMargin,
    this.tabletMargin,
    this.desktopMargin,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveService = ResponsiveService.to;
    
    return Obx(() {
      final deviceType = responsiveService.deviceType.value;
      
      double? width;
      double? height;
      EdgeInsets? padding;
      EdgeInsets? margin;
      
      switch (deviceType) {
        case DeviceType.mobile:
          width = mobileWidth;
          height = mobileHeight;
          padding = mobilePadding;
          margin = mobileMargin;
          break;
        case DeviceType.tablet:
          width = tabletWidth ?? mobileWidth;
          height = tabletHeight ?? mobileHeight;
          padding = tabletPadding ?? mobilePadding;
          margin = tabletMargin ?? mobileMargin;
          break;
        case DeviceType.desktop:
          width = desktopWidth ?? tabletWidth ?? mobileWidth;
          height = desktopHeight ?? tabletHeight ?? mobileHeight;
          padding = desktopPadding ?? tabletPadding ?? mobilePadding;
          margin = desktopMargin ?? tabletMargin ?? mobileMargin;
          break;
      }
      
      return Container(
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: decoration,
        child: child,
      );
    });
  }
}

class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final int mobileColumns;
  final int tabletColumns;
  final int desktopColumns;
  final double spacing;
  final double runSpacing;
  final EdgeInsets? padding;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.mobileColumns = 2,
    this.tabletColumns = 3,
    this.desktopColumns = 4,
    this.spacing = 16.0,
    this.runSpacing = 16.0,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final responsiveService = ResponsiveService.to;
    
    return Obx(() {
      final columns = responsiveService.getGridColumns(
        mobile: mobileColumns,
        tablet: tabletColumns,
        desktop: desktopColumns,
      );
      
      return Padding(
        padding: padding ?? EdgeInsets.zero,
        child: GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: columns,
            crossAxisSpacing: spacing,
            mainAxisSpacing: runSpacing,
          ),
          itemCount: children.length,
          itemBuilder: (context, index) => children[index],
        ),
      );
    });
  }
}
