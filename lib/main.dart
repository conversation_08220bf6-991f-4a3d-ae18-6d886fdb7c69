import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/bottom_navigation/controller/bottom_navigation_controller.dart';
import 'package:openslate/services/connectivity_service.dart';
import 'package:openslate/services/navigation_service.dart';
import 'package:openslate/services/responsive_service.dart';
import 'package:openslate/utils/routes.dart';
import 'common/app_binding.dart';
import 'common/mobilesize/sizeconfig.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  Get.put(NavigationService(), permanent: true);
  Get.put(ConnectivityService(), permanent: true);
  Get.put(ResponsiveService(), permanent: true);
  Get.lazyPut(() => BottomNavigationController(), fenix: true);

  // REQUIRED: Initialize FastCachedImage
  await FastCachedImageConfig.init(
    subDir: 'fast_cached_images',
    clearCacheAfter: const Duration(days: 15),
  );

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      debugShowCheckedModeBanner: false,
      initialRoute: Routes.splashScreen,
      getPages: Routes.getPages,
      initialBinding: AppBindings(),
      builder: (context, child) {
        // Initialize SizeConfig once here instead of in every build
        SizeConfig.init(context);
        // Update responsive service on screen size changes
        ResponsiveService.to.updateScreenInfo();
        return child!;
      },
    );
  }
}
