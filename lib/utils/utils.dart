import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';



class Utils {


  static void fieldFocusChange(BuildContext context , FocusNode current , FocusNode  nextFocus ){
    current.unfocus();
    FocusScope.of(context).requestFocus(nextFocus);
  }




  static snackBar(String title, String message){
    Get.snackbar(
      title,
      message ,
    );
  }



  static pngImage(String pngString, {double? height, double? width}){
    return Image.asset(
      pngString,
      height: height,
      width: width,
      fit: BoxFit.cover,
    );
  }

  static ImageProvider pngImageAsset(String pngString) {
    return AssetImage(pngString);  // Return AssetImage as an ImageProvider
  }

  static svgImage(String pngString, {double? height, double? width}){
    return SvgPicture.asset(
      pngString,
      height: height,
      width: width,
      placeholderBuilder: (BuildContext context) => Container(
        height: height,
        width: width,
        color: Colors.grey[300],
        child: const Icon(Icons.image, color: Colors.grey),
      ),
    );
  }
}


class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    final cleanHex = hexColor.replaceAll("#", "").toUpperCase();
    final buffer = StringBuffer();
    if (cleanHex.length == 6) buffer.write("FF"); // Add opacity if not provided
    buffer.write(cleanHex);
    return int.parse(buffer.toString(), radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));

  static Color fromHex(String hexColor) {
    return HexColor(hexColor);
  }
}

TextStyle appTextStyle({
  double? fontSize,
  FontWeight?fontWeight,
  Color? color,
  TextDecoration? decoration,
  double? height,
  FontStyle? fontStyle,
}) {
  return GoogleFonts.poppins(
    fontSize: fontSize,
    fontWeight: fontWeight,
    color: color,
    decoration: decoration,
    height: height,
    fontStyle: fontStyle,
  );
}

