import 'package:get/get.dart';
import 'package:openslate/screens/search/binding/search_binding.dart';
import 'package:openslate/screens/search/pages/search_screen.dart';
import 'package:openslate/screens/sign_in/bindings/sign_in_binding.dart';
import 'package:openslate/screens/sign_in/pages/sign_in_screen.dart';
import 'package:openslate/screens/projects/pages/project_list_screen.dart';
import 'package:openslate/screens/no_internet/pages/no_internet_screen.dart';
import 'package:openslate/screens/onBoarding/pages/welcome_screen.dart';
import '../screens/bottom_navigation/bindings/bottom_navigation_binding.dart';
import '../screens/my_space/bindings/my_space_binding.dart';
import '../screens/my_space/pages/my_space_screen.dart';
import '../screens/bottom_navigation/pages/bottom_navigation_screens.dart';
import '../screens/sign_up/bindings/sign_up_binding.dart';
import '../screens/sign_up/pages/sign_up_screen.dart';
import '../screens/splash/bindings/splash_binding.dart';
import '../screens/splash/pages/splash_screen.dart';

class Routes {
  Routes._();
  static const String splashScreen = '/';
  static const String bottomNavigationScreen = '/bottomNavigationScreen';
  static const String mySpaceScreen = '/mySpaceScreen';
  static const String projectListScreen = '/projectListScreen';
  static const String signInScreen = '/signInScreen';
  static const String signUpScreen = '/signUpScreen';
  static const String searchScreen = '/searchScreen';
  static const String noInternetScreen = '/no-internet';
  static const String welcomeScreen = '/welcomeScreen';

  static final getPages = [
    GetPage(
        name: Routes.splashScreen,
        page: () =>   SplashScreen(),
        transition: Transition.downToUp,
        binding: SplashBinding(),
    ),
    GetPage(
        name: Routes.bottomNavigationScreen,
        page: () =>   BottomNavigationScreen(),
        transition: Transition.downToUp,
        binding: BottomNavigationBinding(),
    ),
    GetPage(
      name: Routes.mySpaceScreen,
      page: () =>   MySpaceScreen(),
      transition: Transition.downToUp,
      binding: MySpaceBinding(),
    ),
     GetPage(
        name: Routes.signInScreen,
        page: () =>   SignInScreen(),
        transition: Transition.downToUp,
        binding: SignInBinding(),
    ),
    GetPage(
      name: Routes.signUpScreen,
      page: () =>   SignUpScreen(),
      transition: Transition.downToUp,
      binding: SignUpBinding(),
    ),

     GetPage(
        name: Routes.searchScreen,
        page: () =>   SearchScreen(),
        transition: Transition.downToUp,
        binding: SearchBinding(),
    ),
    GetPage(
      name: Routes.projectListScreen,
      page: () =>   ProjectListScreen(),
      transition: Transition.downToUp,
      //binding: MySpaceBinding(),
    ),
    GetPage(
      name: Routes.noInternetScreen,
      page: () => const NoInternetScreen(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.welcomeScreen,
      page: () => const WelcomeScreen(),
      transition: Transition.fadeIn,
    ),
  ];
}
