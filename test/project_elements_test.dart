import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:openslate/screens/projects/pages/project_detail_screen.dart';
import 'package:openslate/models/project_model.dart';

void main() {
  group('Project Elements Tests', () {
    late ProjectDetailController controller;

    setUp(() {
      Get.testMode = true;
      controller = ProjectDetailController();
      controller.project = ProjectModel.sample();
    });

    tearDown(() {
      Get.reset();
    });

    test('should initialize with info tab selected', () {
      expect(controller.selectedTabIndex.value, 0);
      expect(controller.selectedElement.value, null);
    });

    test('should show elements when showElements is called', () {
      controller.showElements();
      expect(controller.selectedTabIndex.value, 1);
    });

    test('should hide elements and clear selection when hideElements is called', () {
      // First show elements and select one
      controller.showElements();
      controller.selectElement(controller.project.elements.first);

      expect(controller.selectedTabIndex.value, 1);
      expect(controller.selectedElement.value, isNotNull);

      // Then hide elements
      controller.hideElements();

      expect(controller.selectedTabIndex.value, 0);
      expect(controller.selectedElement.value, null);
    });

    test('should select element when selectElement is called', () {
      final element = controller.project.elements.first;
      controller.selectElement(element);
      
      expect(controller.selectedElement.value, element);
    });

    test('should have sample elements with correct properties', () {
      final elements = controller.project.elements;
      
      expect(elements.length, greaterThan(0));
      
      final firstElement = elements.first;
      expect(firstElement.name, isNotEmpty);
      expect(firstElement.brand, isNotEmpty);
      expect(firstElement.category, isNotEmpty);
      expect(firstElement.price, isNotEmpty);
      expect(firstElement.images, isNotEmpty);
      expect(firstElement.x, greaterThanOrEqualTo(0));
      expect(firstElement.x, lessThanOrEqualTo(1));
      expect(firstElement.y, greaterThanOrEqualTo(0));
      expect(firstElement.y, lessThanOrEqualTo(1));
    });
  });
}
