PODS:
  - connectivity_plus (0.0.1):
    - Flutter
  - Flutter (1.0.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"

SPEC CHECKSUMS:
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_keyboard_visibility: 4625131e43015dbbe759d9b20daaf77e0e3f6619
  geolocator_apple: ab36aa0e8b7d7a2d7639b3b4e48308394e8cef5e
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564

PODFILE CHECKSUM: 4305caec6b40dde0ae97be1573c53de1882a07e5

COCOAPODS: 1.16.2
